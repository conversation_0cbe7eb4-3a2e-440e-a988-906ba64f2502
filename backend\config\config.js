const { Sequelize } = require('sequelize');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// --- 环境变量加载逻辑 (与 app.js 保持一致) ---
const NODE_ENV = process.env.NODE_ENV || 'development';

// 定义 .env 文件加载顺序
const envFiles = [
  `.env.${NODE_ENV}.local`,
  `.env.${NODE_ENV}`,
  '.env.local',
  '.env'
];

envFiles.forEach(file => {
  const filePath = path.resolve(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    dotenv.config({
      path: filePath,
      override: true
    });
  }
});
// --- 环境变量加载逻辑结束 ---

// 数据库配置
const dbConfig = {
  database: process.env.DB_NAME || 'health_db',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'zn123',
  host: process.env.DB_HOST || '************',
  port: process.env.DB_PORT || 3306,
  dialect: 'mysql'
};



const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: false, // 设置为 true 可以看到 SQL 查询日志
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
);

// 测试数据库连接
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功！');
  } catch (error) {
    console.error('数据库连接失败:', error);
  }
}

module.exports = {
  sequelize,
  testConnection
};