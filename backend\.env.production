# 数据库配置 - 生产环境
DB_HOST=mysql
DB_PORT=3306
DB_NAME=health_uplink
DB_USER=health_user
DB_PASSWORD=health_user

# JWT配置
JWT_SECRET=a_very_strong_and_long_secret_key_for_production

# 服务器配置
PORT=3000
NODE_ENV=production
API_BASE_URL=https://your.domain.com/api

# 数据库同步配置 (生产环境强烈建议禁用)
DB_SYNC_ENABLED=false
DB_SYNC_FORCE=false
DB_SYNC_ALTER=false

# 文件上传配置
UPLOAD_DIR=uploads/images
MAX_FILE_SIZE=10485760

# 日志配置
LOG_LEVEL=warn # 生产环境记录更少日志
LOG_DIR=log/backend

# 管理员账户现在通过数据库初始化脚本创建

# API限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100