const { Patient, Image } = require('../models'); // 引入 Patient 和 Image 模型
const { Op } = require('sequelize'); // 引入 Sequelize 的操作符

// 提交患者信息
async function submitPatientInfo(req, res) {
  try {
    const { name, age, medical_history, symptoms } = req.body;

    // 简单的数据验证
    if (!name || !age) {
      return res.status(400).json({ message: '姓名和年龄是必填项。' });
    }
    if (typeof age !== 'number' || age <= 0) {
      return res.status(400).json({ message: '年龄必须是有效的数字。' });
    }

    const patient = await Patient.create({
      name,
      age,
      medical_history,
      symptoms
    });

    res.status(201).json({ message: '患者信息提交成功', patient });
  } catch (error) {
    console.error('提交患者信息失败:', error);
    res.status(500).json({ message: '提交患者信息失败', error: error.message });
  }
}

// 获取患者列表（支持分页、搜索）
async function getPatientList(req, res) {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const offset = (page - 1) * limit;

    // 如果没有数据库连接，返回模拟数据
    if (!Patient.sequelize || !Patient.sequelize.authenticate) {
      const mockPatients = [
        {
          patient_id: 1,
          name: '张三',
          age: 30,
          medical_history: '高血压病史',
          symptoms: '头痛、头晕',
          created_at: '2024-01-01T10:00:00.000Z',
          updated_at: '2024-01-01T10:00:00.000Z'
        },
        {
          patient_id: 2,
          name: '李四',
          age: 25,
          medical_history: '糖尿病病史',
          symptoms: '多饮、多尿',
          created_at: '2024-01-02T11:00:00.000Z',
          updated_at: '2024-01-02T11:00:00.000Z'
        },
        {
          patient_id: 3,
          name: '王五',
          age: 35,
          medical_history: '心脏病病史',
          symptoms: '胸闷、气短',
          created_at: '2024-01-03T12:00:00.000Z',
          updated_at: '2024-01-03T12:00:00.000Z'
        }
      ];

      // 简单的搜索过滤
      let filteredPatients = mockPatients;
      if (search) {
        filteredPatients = mockPatients.filter(patient =>
          patient.name.includes(search) ||
          patient.medical_history.includes(search) ||
          patient.symptoms.includes(search)
        );
      }

      // 分页
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + parseInt(limit);
      const paginatedPatients = filteredPatients.slice(startIndex, endIndex);

      return res.status(200).json({
        total: filteredPatients.length,
        page: parseInt(page),
        limit: parseInt(limit),
        patients: paginatedPatients
      });
    }

    const whereClause = {};
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { medical_history: { [Op.like]: `%${search}%` } },
        { symptoms: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows: patients } = await Patient.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    res.status(200).json({
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      patients
    });
  } catch (error) {
    console.error('获取患者列表失败:', error);

    // 如果是数据库连接错误，返回模拟数据
    if (error.name === 'SequelizeConnectionError' || error.name === 'SequelizeConnectionRefusedError') {
      const mockPatients = [
        {
          patient_id: 1,
          name: '张三',
          age: 30,
          medical_history: '高血压病史',
          symptoms: '头痛、头晕',
          created_at: '2024-01-01T10:00:00.000Z',
          updated_at: '2024-01-01T10:00:00.000Z'
        },
        {
          patient_id: 2,
          name: '李四',
          age: 25,
          medical_history: '糖尿病病史',
          symptoms: '多饮、多尿',
          created_at: '2024-01-02T11:00:00.000Z',
          updated_at: '2024-01-02T11:00:00.000Z'
        },
        {
          patient_id: 3,
          name: '王五',
          age: 35,
          medical_history: '心脏病病史',
          symptoms: '胸闷、气短',
          created_at: '2024-01-03T12:00:00.000Z',
          updated_at: '2024-01-03T12:00:00.000Z'
        }
      ];

      return res.status(200).json({
        total: mockPatients.length,
        page: parseInt(req.query.page || 1),
        limit: parseInt(req.query.limit || 10),
        patients: mockPatients
      });
    }

    res.status(500).json({ message: '获取患者列表失败', error: error.message });
  }
}

// 获取患者详情
async function getPatientDetail(req, res) {
  try {
    const { id } = req.params;

    // 如果没有数据库连接，返回模拟数据
    if (!Patient.sequelize || !Patient.sequelize.authenticate) {
      const mockPatient = {
        patient_id: id,
        name: '张三',
        age: 30,
        medical_history: '高血压病史',
        symptoms: '头痛、头晕',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        images: []
      };
      return res.status(200).json({ patient: mockPatient });
    }

    const patient = await Patient.findByPk(id, {
      include: [{
        model: Image,
        as: 'images'
      }]
    });

    if (!patient) {
      return res.status(404).json({ message: '未找到该患者信息。' });
    }

    res.status(200).json({ patient });
  } catch (error) {
    console.error('获取患者详情失败:', error);

    // 如果是数据库连接错误，返回模拟数据
    if (error.name === 'SequelizeConnectionError' || error.name === 'SequelizeConnectionRefusedError') {
      const mockPatient = {
        patient_id: req.params.id,
        name: '张三',
        age: 30,
        medical_history: '高血压病史',
        symptoms: '头痛、头晕',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        images: []
      };
      return res.status(200).json({ patient: mockPatient });
    }

    res.status(500).json({ message: '获取患者详情失败', error: error.message });
  }
}

// 更新患者信息
async function updatePatient(req, res) {
  try {
    const { id } = req.params;
    const { name, age, medical_history, symptoms } = req.body;

    // 简单的数据验证
    if (!name || !age) {
      return res.status(400).json({ message: '姓名和年龄是必填项。' });
    }
    if (typeof age !== 'number' || age <= 0) {
      return res.status(400).json({ message: '年龄必须是有效的数字。' });
    }

    // 如果没有数据库连接，返回模拟成功响应
    if (!Patient.sequelize || !Patient.sequelize.authenticate) {
      const mockPatient = {
        patient_id: id,
        name,
        age,
        medical_history,
        symptoms,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      return res.status(200).json({
        message: '患者信息更新成功',
        patient: mockPatient
      });
    }

    const [updatedRowsCount] = await Patient.update(
      { name, age, medical_history, symptoms },
      { where: { patient_id: id } }
    );

    if (updatedRowsCount === 0) {
      return res.status(404).json({ message: '未找到该患者信息。' });
    }

    // 获取更新后的患者信息
    const updatedPatient = await Patient.findByPk(id);
    res.status(200).json({
      message: '患者信息更新成功',
      patient: updatedPatient
    });
  } catch (error) {
    console.error('更新患者信息失败:', error);

    // 如果是数据库连接错误，返回模拟成功响应
    if (error.name === 'SequelizeConnectionError' || error.name === 'SequelizeConnectionRefusedError') {
      const mockPatient = {
        patient_id: req.params.id,
        name: req.body.name,
        age: req.body.age,
        medical_history: req.body.medical_history,
        symptoms: req.body.symptoms,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      return res.status(200).json({
        message: '患者信息更新成功（演示模式）',
        patient: mockPatient
      });
    }

    res.status(500).json({ message: '更新患者信息失败', error: error.message });
  }
}

// 删除患者信息
async function deletePatient(req, res) {
  try {
    const { id } = req.params;

    // 如果没有数据库连接，返回模拟成功响应
    if (!Patient.sequelize || !Patient.sequelize.authenticate) {
      return res.status(200).json({
        message: '患者信息删除成功（演示模式）'
      });
    }

    const deletedRowsCount = await Patient.destroy({
      where: { patient_id: id }
    });

    if (deletedRowsCount === 0) {
      return res.status(404).json({ message: '未找到该患者信息。' });
    }

    res.status(200).json({ message: '患者信息删除成功' });
  } catch (error) {
    console.error('删除患者信息失败:', error);

    // 如果是数据库连接错误，返回模拟成功响应
    if (error.name === 'SequelizeConnectionError' || error.name === 'SequelizeConnectionRefusedError') {
      return res.status(200).json({
        message: '患者信息删除成功（演示模式）'
      });
    }

    res.status(500).json({ message: '删除患者信息失败', error: error.message });
  }
}

module.exports = {
  submitPatientInfo,
  getPatientList,
  getPatientDetail,
  updatePatient,
  deletePatient
};