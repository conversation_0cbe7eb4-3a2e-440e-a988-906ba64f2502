const { sequelize } = require('../config/config');
const Patient = require('./patient');
const Image = require('./image');
const Doctor = require('./doctor');

// 设置模型关联关系
Patient.hasMany(Image, {
  foreignKey: 'patient_id',
  as: 'images'
});

Image.belongsTo(Patient, {
  foreignKey: 'patient_id',
  as: 'patient'
});

// 导出所有模型和 sequelize 实例
module.exports = {
  sequelize,
  Patient,
  Image,
  Doctor
};