<template>
  <ContentContainer>
    <h2>数据导出</h2>
    <el-card>
      <template #header>
        <div class="card-header">
          <span>导出选项</span>
        </div>
      </template>
      <el-form :model="exportForm" label-width="120px">
        <el-form-item label="导出类型">
          <el-select v-model="exportForm.type" placeholder="请选择导出类型">
            <el-option label="患者信息 (CSV)" value="csv"></el-option>
            <el-option label="图片资料 (ZIP)" value="images"></el-option>
            <el-option label="完整数据包 (CSV + 图片ZIP)" value="all"></el-option>
          </el-select>
        </el-form-item>
        <!-- 可以根据需要添加其他筛选条件，例如日期范围等 -->
        <el-form-item>
          <el-button type="primary" @click="handleExport" :loading="loading">开始导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card v-if="exportUrl" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>导出结果</span>
        </div>
      </template>
      <p>导出已完成。点击下方链接下载：</p>
      <el-link :href="exportUrl" type="primary" target="_blank">{{ exportFileName }}</el-link>
    </el-card>
  </ContentContainer>
</template>

<script>
import { defineComponent, ref } from 'vue';
import api from '../api';
import ContentContainer from '../components/ContentContainer.vue';

export default defineComponent({
  components: {
    ContentContainer
  },
  setup() {
    const exportForm = ref({
      type: 'csv' // 默认导出 CSV
    });
    const loading = ref(false);
    const exportUrl = ref('');
    const exportFileName = ref('');

    const handleExport = async () => {
      loading.value = true;
      exportUrl.value = ''; // 清空上次的导出链接
      exportFileName.value = '';

      try {
        let endpoint = '';
        let filename = '';
        switch (exportForm.value.type) {
          case 'csv':
            endpoint = '/export/csv';
            filename = 'patients.csv';
            break;
          case 'images':
            endpoint = '/export/images';
            filename = 'images.zip';
            break;
          case 'all':
            endpoint = '/export/all';
            filename = 'health_data_full.zip';
            break;
          default:
            alert('请选择有效的导出类型。');
            loading.value = false;
            return;
        }

        // 使用原生 fetch 或 window.open 进行文件下载，因为 axios 默认处理 JSON 响应
        // 或者配置 axios 响应类型为 blob
        const response = await api.get(endpoint, {
          responseType: 'blob' // 关键：将响应类型设置为 blob
        });

        // 创建 blob URL 并提供下载链接
        const blob = new Blob([response], { type: response.type });
        exportUrl.value = URL.createObjectURL(blob);
        exportFileName.value = filename;

        // 也可以直接触发下载
        // const link = document.createElement('a');
        // link.href = exportUrl.value;
        // link.download = filename;
        // link.click();
        // URL.revokeObjectURL(exportUrl.value); // 释放 blob URL

      } catch (error) {
        console.error('导出失败:', error);
        alert('导出失败，请稍后再试。');
      } finally {
        loading.value = false;
      }
    };

    return {
      exportForm,
      loading,
      exportUrl,
      exportFileName,
      handleExport
    };
  }
});
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}
</style>
