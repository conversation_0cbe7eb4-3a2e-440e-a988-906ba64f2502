import axios from 'axios';

// 创建 Axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || (import.meta.env.PROD ? '/api' : 'http://localhost:3000'), // 生产环境使用/api前缀，开发环境使用localhost
  timeout: 5000 // 请求超时时间
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    console.log('=== API 请求拦截器 ===');
    console.log('请求URL:', config.url);
    console.log('请求方法:', config.method);

    const token = localStorage.getItem('token'); // 从本地存储获取 token
    console.log('本地存储的token:', token ? token.substring(0, 20) + '...' : 'null');

    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token; // 将 token 添加到请求头
      console.log('已添加Authorization头');
    } else {
      console.log('未找到token，跳过Authorization头');
    }

    console.log('最终请求头:', config.headers);
    return config;
  },
  error => {
    // 对请求错误做些什么
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    const res = response.data;

    // 检查HTTP状态码
    if (response.status >= 200 && response.status < 300) {
      return res; // 返回实际的数据部分
    } else {
      console.error('响应错误:', res.message || '请求失败');
      return Promise.reject(new Error(res.message || '请求失败'));
    }
  },
  error => {
    // 对响应错误做点什么
    console.error('响应拦截器错误:', error);

    // 处理不同的 HTTP 状态码错误
    if (error.response) {
      const { status, data } = error.response;

      if (status === 401) {
        // 认证失败，清除token并跳转到登录页
        localStorage.removeItem('token');
        window.location.href = '/login';
        return Promise.reject(new Error('认证失败，请重新登录'));
      } else if (status === 403) {
        return Promise.reject(new Error('权限不足'));
      } else if (status === 404) {
        return Promise.reject(new Error('请求的资源不存在'));
      } else if (status >= 500) {
        return Promise.reject(new Error('服务器错误，请稍后重试'));
      } else {
        return Promise.reject(new Error(data?.message || '请求失败'));
      }
    } else if (error.request) {
      return Promise.reject(new Error('网络错误，请检查网络连接'));
    } else {
      return Promise.reject(new Error('请求配置错误'));
    }
  }
);

export default service;