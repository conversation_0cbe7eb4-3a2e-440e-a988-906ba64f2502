// pages/upload/upload.js
const api = require('../../utils/api.js');
import { initSwipeBack } from '../../utils/swipeBack';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    images: [], // 存储图片路径和类型 {path: '', type: '', uploading: false}
    imageTypes: ['检查单', '患处照片', '其他'],
    selectedType: '检查单',
    isUploading: false, // 添加上传加载状态
    contentPaddingTop: 0, // Add data property for content padding
    hasLoadedFromCache: false // 标记是否已从缓存加载过数据
  },

  // 测试网络连接
  testNetworkConnection: function() {
    console.log('=== 测试网络连接 ===');
    wx.request({
      url: 'http://localhost:3000/health',
      method: 'GET',
      timeout: 5000,
      success: (res) => {
        console.log('✅ 网络连接正常:', res);
        wx.showToast({
          title: '网络连接正常',
          icon: 'success',
          duration: 1000
        });
      },
      fail: (err) => {
        console.error('❌ 网络连接失败:', err);
        wx.showModal({
          title: '网络连接失败',
          content: '无法连接到服务器，请检查：\n1. 后台服务是否启动\n2. 网络设置是否正确\n3. 防火墙设置',
          showCancel: false
        });
      }
    });
  },

  // 辅助函数：获取图片显示URL
  getImageDisplayUrl: function(imagePath) {
    if (!imagePath) return '';
    // 如果是HTTP URL，直接返回
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    // 如果是本地临时文件，直接返回
    if (imagePath.startsWith('wxfile://')) {
      return imagePath;
    }
    // 如果是相对路径，拼接服务器地址
    if (imagePath.startsWith('/')) {
      return 'http://localhost:3000' + imagePath;
    }
    return imagePath;
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('🚀 页面加载开始');

    // 测试网络连接
    this.testNetworkConnection();

    // 清空本地缓存，避免显示重复的待上传图片
    wx.removeStorageSync('uploadedImages');
    console.log('🗑️ 已清空本地缓存的图片数据');

    // 初始化为空数组
    this.setData({
      images: []
    });

    // 获取系统信息，计算导航栏高度
    const { statusBarHeight } = wx.getSystemInfoSync();
    const navigationBarHeight = (statusBarHeight || 0) + 44; // 44px is the default navigation bar height
    this.setData({
      contentPaddingTop: navigationBarHeight // Set content padding
    });

    // 初始化右滑返回功能
    initSwipeBack(this);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时检查是否有缓存的待上传图片
    // 但只在页面首次加载时恢复，避免重复添加
    if (!this.data.hasLoadedFromCache) {
      const savedImages = wx.getStorageSync('uploadedImages');
      if (savedImages && savedImages.length > 0) {
        // 只加载未上传的图片
        const pendingImages = savedImages.filter(img => !img.uploaded);
        if (pendingImages.length > 0) {
          console.log('📷 从缓存恢复待上传图片:', pendingImages.length);
          this.setData({
            images: pendingImages, // 直接设置，不要合并
            hasLoadedFromCache: true
          });
        }
      } else {
        this.setData({
          hasLoadedFromCache: true
        });
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    // 页面隐藏时只保存未上传的图片到本地缓存
    const pendingImages = this.data.images.filter(img => !img.uploaded);
    if (pendingImages.length > 0) {
      wx.setStorageSync('uploadedImages', pendingImages);
      console.log('💾 保存待上传图片到缓存:', pendingImages.length);
    } else {
      wx.removeStorageSync('uploadedImages');
      console.log('🗑️ 清空缓存（无待上传图片）');
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },

  chooseImage: function () {
    const maxImages = 9; // 最大允许上传图片数量
    const currentImageCount = this.data.images.length;
    const remainingCount = maxImages - currentImageCount;

    if (remainingCount <= 0) {
      wx.showToast({
        title: `最多只能上传${maxImages}张图片`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.chooseImage({
      count: remainingCount, // 限制选择数量
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        console.log('📷 wx.chooseImage success:', res);
        console.log('📁 tempFilePaths:', res.tempFilePaths);
        console.log('📄 tempFiles详情:', res.tempFiles);

        const validImages = [];
        const invalidFiles = [];
        const maxSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];

        // 处理选择的文件
        const processFiles = res.tempFiles || [];
        const tempPaths = res.tempFilePaths || [];

        processFiles.forEach((file, index) => {
          console.log(`🔍 处理文件 ${index + 1}:`, {
            'file.path': file.path,
            'file.size': file.size,
            'tempFilePaths[index]': tempPaths[index]
          });

          // 优先使用 tempFilePaths，因为它通常是正确的路径
          let filePath = tempPaths[index];
          if (!filePath && file.path) {
            filePath = file.path;
          }

          if (!filePath) {
            console.error('❌ 无法获取文件路径');
            return;
          }

          console.log(`📍 最终使用路径: ${filePath}`);

          const fileExtension = filePath.split('.').pop().toLowerCase();

          if (file.size > maxSize || !allowedTypes.includes(fileExtension)) {
            invalidFiles.push(filePath);
            console.log('❌ 文件不符合要求:', filePath);
          } else {
            const imageObj = {
              path: filePath,
              type: this.data.selectedType,
              uploaded: false,
              uploading: false,
              progress: 0
            };
            validImages.push(imageObj);
            console.log('✅ 文件有效:', imageObj);
          }
        });

        if (invalidFiles.length > 0) {
          wx.showToast({
            title: `以下文件不符合要求（大小超过5MB或格式不支持）：\n${invalidFiles.map(f => f.split('/').pop()).join('\n')}`,
            icon: 'none',
            duration: 3000
          });
        }

        if (validImages.length > 0) {
          const newImages = this.data.images.concat(validImages);
          this.setData({
            images: newImages
          });
          wx.setStorageSync('uploadedImages', newImages); // 更新本地缓存

          wx.showToast({
            title: `已选择${validImages.length}张图片`,
            icon: 'success',
            duration: 1500
          });
        }
      },
      fail: (err) => {
        console.error('wx.chooseImage fail:', err); // 添加日志
        api.showErrorToast('选择图片失败');
      }
    });
  },

  batchUpload: function () {
    // 批量上传按钮现在触发上传操作
    this.uploadImages();
  },
  // 图片加载成功事件
  onImageLoad: function(e) {
    const index = e.currentTarget.dataset.index;
    const image = this.data.images[index];
    console.log(`✅ 图片 ${index} 加载成功:`, {
      detail: e.detail,
      imagePath: image ? image.path : 'unknown',
      processedUrl: this.getImageDisplayUrl(image ? image.path : '')
    });
  },

  // 图片加载失败事件
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const image = this.data.images[index];
    console.error(`❌ 图片 ${index} 加载失败:`, {
      detail: e.detail,
      imagePath: image ? image.path : 'unknown',
      processedUrl: this.getImageDisplayUrl(image ? image.path : ''),
      imageInfo: image
    });

    // 不显示toast，避免频繁弹窗干扰用户
    // wx.showToast({
    //   title: '图片加载失败',
    //   icon: 'none',
    //   duration: 2000
    // });
  },

  previewImage: function (e) {
    const currentSrc = e.currentTarget.dataset.src;
    // 处理图片URL，确保预览时使用正确的路径
    const urls = this.data.images.map(img => {
      let imagePath = img.path;
      // 如果是已上传的图片（HTTP URL），直接使用
      if (imagePath.startsWith('http')) {
        return imagePath;
      }
      // 如果是本地临时文件，直接使用
      return imagePath;
    });

    wx.previewImage({
      current: currentSrc,
      urls: urls
    });
  },

  deleteImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const imageToDelete = this.data.images[index];

    // 显示确认对话框
    wx.showModal({
      title: '删除确认',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          let images = [...this.data.images]; // 创建副本
          images.splice(index, 1);
          this.setData({
            images: images
          });

          // 如果图片还未上传，直接从缓存中删除
          if (!imageToDelete.uploaded) {
            wx.setStorageSync('uploadedImages', images);
          }

          wx.showToast({
            title: '图片已删除',
            icon: 'success',
            duration: 1500
          });

          console.log('🗑️ 删除图片:', imageToDelete.path);
        }
      }
    });
  },

  bindPickerChange: function (e) {
    this.setData({
      selectedType: this.data.imageTypes[e.detail.value]
    });
  },

  // 获取图片显示URL的辅助方法
  getImageDisplayUrl: function(path) {
    if (!path || typeof path !== 'string' || path === '') {
      return '';
    }

    // 如果是HTTP URL，直接返回
    if (path.startsWith('http')) {
      return path;
    }

    // 如果是微信临时文件路径（各种格式）
    if (path.startsWith('wxfile://') ||
        path.startsWith('tmp_') ||
        path.includes('temp/') ||
        path.startsWith('store_') ||
        path.includes('/var/mobile/') ||
        path.startsWith('file://')) {
      return path;
    }

    // 如果是相对路径，拼接服务器地址
    if (path.startsWith('/')) {
      return 'http://localhost:3000' + path;
    }

    return path;
  },

  // 清空所有图片
  clearAllImages: function () {
    wx.showModal({
      title: '清空确认',
      content: '确定要清空所有图片吗？此操作不可恢复。',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            images: [],
            hasLoadedFromCache: false // 重置缓存标志
          });
          wx.removeStorageSync('uploadedImages');
          wx.showToast({
            title: '已清空所有图片',
            icon: 'success',
            duration: 1500
          });
          console.log('🗑️ 清空所有图片');
        }
      }
    });
  },

  uploadImages: function () {
    const imagesToUpload = this.data.images.filter(img => !img.uploaded && !img.uploading);

    if (imagesToUpload.length === 0) {
      wx.showToast({
        title: '没有新图片需要上传',
        icon: 'none'
      });
      return;
    }

    this.setData({ isUploading: true }); // 显示上传加载状态

    let uploadPromises = imagesToUpload.map(image => {
      console.log('准备上传图片:', image.path); // 添加日志
      // 标记图片为正在上传，并初始化进度
      const updatingImages = this.data.images.map(img =>
         img.path === image.path ? { ...img, uploading: true, progress: 0 } : img
      );
      this.setData({ images: updatingImages });
      // 不在每次更新进度时都更新本地缓存，只在上传成功/失败时更新

      return api.uploadImage(image.path, image.type, null, (progress) => {
        // 更新图片上传进度
        const progressImages = this.data.images.map(img =>
          img.path === image.path ? { ...img, progress: progress } : img
        );
        this.setData({ images: progressImages });
      })
        .then(res => {
          console.log('图片上传成功:', image.path, res); // 添加日志
          // 标记为已上传，移除 uploading 标记，进度设为 100
          // 后台返回的是 images 数组，取第一个图片的 image_url
          let uploadedImageUrl = image.path; // 默认保持原路径
          if (res.images && res.images.length > 0) {
            const imageUrl = res.images[0].image_url;
            // 如果是相对路径，拼接完整URL
            if (imageUrl.startsWith('/')) {
              uploadedImageUrl = 'http://localhost:3000' + imageUrl;
            } else {
              uploadedImageUrl = imageUrl;
            }
          }

          const updatedImages = this.data.images.map(img =>
            img.path === image.path ? {
              ...img,
              path: uploadedImageUrl,
              uploaded: true,
              uploading: false,
              progress: 100
            } : img
          );

          console.log('=== 图片上传成功处理 ===');
          console.log('原始图片路径:', image.path);
          console.log('新的图片URL:', uploadedImageUrl);
          console.log('更新后的图片数组:', updatedImages);

          this.setData({ images: updatedImages }, () => {
            console.log('setData完成，当前data.images:', this.data.images);
          });
          wx.setStorageSync('uploadedImages', updatedImages); // 更新本地缓存
          return res;
        })
        .catch(err => {
          // 上传失败，移除 uploading 标记，不标记为已上传，进度设为 0
          console.error('图片上传失败:', image.path, err); // 已有日志，保留
           const failedImages = this.data.images.map(img =>
            img.path === image.path ? { ...img, uploading: false, progress: 0 } : img
          );
          this.setData({ images: failedImages });
          wx.setStorageSync('uploadedImages', failedImages); // 更新本地缓存
          console.log('上传失败后 images 数组:', this.data.images); // 添加日志
          // API 层已处理错误提示，这里不再重复
          throw err; // 抛出错误以便 Promise.all 捕获
        });
    });

    Promise.all(uploadPromises)
      .then(results => {
        wx.showToast({
          title: '所有图片上传成功',
          icon: 'success',
          duration: 2000
        });
      })
      .catch(err => {
        // Promise.all 只要有一个失败就会进入这里
        // 具体的错误提示已在 api.js 或单个图片的 catch 中处理
        console.error('批量图片上传过程中发生错误', err);
      })
      .finally(() => {
        this.setData({ isUploading: false }); // 隐藏上传加载状态
      });
  }
})
