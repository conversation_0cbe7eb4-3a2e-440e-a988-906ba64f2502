const express = require('express');
const router = express.Router();
const logController = require('../controllers/log');
const { authenticateToken } = require('../middlewares/auth'); // 引入认证中间件

// GET /api/log/files - 获取可用日志文件列表 (需要认证)
router.get('/files', authenticateToken, logController.getLogFiles);

// GET /api/log/:filename - 获取指定日志文件内容 (需要认证)
router.get('/:filename', authenticateToken, logController.getLogContent);

module.exports = router;