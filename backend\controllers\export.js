const { Patient, Image } = require('../models');
const { createObjectCsvStringifier } = require('csv-writer');
const archiver = require('archiver');
const path = require('path');
const fs = require('fs');

// 导出患者信息为 CSV 格式
async function exportPatientsToCsv(req, res) {
  try {
    const patients = await Patient.findAll({
      order: [['created_at', 'ASC']]
    });

    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'patient_id', title: '患者ID' },
        { id: 'name', title: '姓名' },
        { id: 'age', title: '年龄' },
        { id: 'medical_history', title: '病史' },
        { id: 'symptoms', title: '症状' },
        { id: 'created_at', title: '创建时间' },
        { id: 'updated_at', title: '更新时间' }
      ]
    });

    const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(patients);

    res.header('Content-Type', 'text/csv');
    res.attachment('patients.csv');
    res.send(csvContent);
  } catch (error) {
    console.error('导出患者信息为 CSV 失败:', error);
    res.status(500).json({ message: '导出患者信息为 CSV 失败', error: error.message });
  }
}

// 导出图片为 ZIP 压缩包
async function exportImagesToZip(req, res) {
  try {
    const images = await Image.findAll();

    if (images.length === 0) {
      return res.status(404).json({ message: '没有图片可供导出。' });
    }

    const archive = archiver('zip', {
      zlib: { level: 9 } // 设置压缩级别
    });

    res.header('Content-Type', 'application/zip');
    res.attachment('images.zip');
    archive.pipe(res);

    for (const image of images) {
      const imagePath = path.join(__dirname, '..', image.image_url);
      if (fs.existsSync(imagePath)) {
        archive.file(imagePath, { name: path.basename(image.image_url) });
      } else {
        console.warn(`图片文件不存在: ${imagePath}`);
      }
    }

    archive.finalize();
  } catch (error) {
    console.error('导出图片为 ZIP 失败:', error);
    res.status(500).json({ message: '导出图片为 ZIP 失败', error: error.message });
  }
}

// 导出完整数据包（CSV + 图片ZIP）
async function exportAllData(req, res) {
  try {
    // 1. 生成 CSV 内容
    const patients = await Patient.findAll({
      order: [['created_at', 'ASC']]
    });
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'patient_id', title: '患者ID' },
        { id: 'name', title: '姓名' },
        { id: 'age', title: '年龄' },
        { id: 'medical_history', title: '病史' },
        { id: 'symptoms', title: '症状' },
        { id: 'created_at', title: '创建时间' },
        { id: 'updated_at', title: '更新时间' }
      ]
    });
    const csvContent = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(patients);

    // 2. 准备 ZIP 归档
    const archive = archiver('zip', {
      zlib: { level: 9 }
    });

    res.header('Content-Type', 'application/zip');
    res.attachment('health_data_full.zip');
    archive.pipe(res);

    // 添加 CSV 文件到 ZIP
    archive.append(csvContent, { name: 'patients.csv' });

    // 添加图片文件到 ZIP
    const images = await Image.findAll();
    for (const image of images) {
      const imagePath = path.join(__dirname, '..', image.image_url);
      if (fs.existsSync(imagePath)) {
        archive.file(imagePath, { name: `images/${path.basename(image.image_url)}` });
      } else {
        console.warn(`图片文件不存在 (完整导出): ${imagePath}`);
      }
    }

    archive.finalize();

  } catch (error) {
    console.error('导出完整数据包失败:', error);
    res.status(500).json({ message: '导出完整数据包失败', error: error.message });
  }
}

module.exports = {
  exportPatientsToCsv,
  exportImagesToZip,
  exportAllData
};