# Health Uplink 项目操作指南

## 1. 项目概述

Health Uplink 项目包含以下主要部分：

-   **后台 (Backend)**: 基于 Node.js/Express 的 API 服务器，负责数据存储、业务逻辑和 API 接口。
-   **前端 (Frontend)**: 基于 Vue 3/Vite 的管理界面，供管理员用户使用。

## 2. 环境要求

在部署和运行项目之前，请确保服务器环境满足以下要求：

-   Node.js (建议使用 LTS 版本)
-   npm (Node.js 安装时通常会包含)
-   MySQL 数据库
-   Git (用于克隆项目代码)

## 3. 后台项目启动

### 3.1 获取代码

克隆项目代码到服务器：

```bash
git clone <项目仓库地址>
cd backend
```

### 3.2 安装依赖

在 `backend/` 目录下运行 npm 安装命令：

```bash
npm install
```

### 3.3 配置环境变量

在 `backend/` 目录下创建 `.env` 文件，并配置数据库连接、JWT 密钥等信息。示例：

```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=password
DB_NAME=health_uplink_db
DB_PORT=3306
JWT_SECRET=your_jwt_secret
NODE_ENV=production # 生产环境请设置为 production

# 数据库同步配置
DB_SYNC_ENABLED=false # 生产环境建议设置为 false
DB_SYNC_FORCE=false # 生产环境务必设置为 false
DB_SYNC_ALTER=false # 生产环境务必设置为 false
```

请根据实际数据库配置修改这些值。

#### 数据库同步配置说明

- `DB_SYNC_ENABLED`: 是否启用数据库同步
  - `true`: 启用自动同步（开发环境推荐）
  - `false`: 禁用自动同步（生产环境推荐）

- `DB_SYNC_FORCE`: 强制同步模式
  - `true`: 删除现有表并重新创建（⚠️ 会丢失所有数据）
  - `false`: 不使用强制模式（推荐）

- `DB_SYNC_ALTER`: 结构更新模式
  - `true`: 修改现有表结构以匹配模型定义（谨慎使用）
  - `false`: 不修改现有表结构（推荐）

### 3.4 初始化数据库

#### 开发环境
在开发环境中，可以启用数据库同步：

```env
DB_SYNC_ENABLED=true
DB_SYNC_FORCE=false  # 或 true（如需重建表）
```

#### 生产环境
在生产环境中，建议禁用自动同步，手动管理数据库迁移：

```env
DB_SYNC_ENABLED=false
DB_SYNC_FORCE=false
DB_SYNC_ALTER=false
```

如果是首次部署，可以临时启用同步：

```bash
cd backend
DB_SYNC_ENABLED=true node app.js
```

启动完成后，建议将 `DB_SYNC_ENABLED` 设置回 `false`。

### 3.5 启动后台应用

在开发环境中，可以直接使用 `node` 命令启动：

```bash
cd backend && node app.js
cd backend && node --inspect app.js


```

在生产环境中，建议使用进程管理工具（如 PM2）来启动和管理应用，以确保应用稳定运行和自动重启。

## 4. 前端项目启动

### 4.1 获取代码

如果尚未克隆整个项目，请先克隆。然后切换到前端项目目录：

```bash
cd frontend
```

### 4.2 安装依赖

在 `frontend/` 目录下运行 npm 安装命令：

```bash
npm install
```

### 4.3 构建前端应用

在生产环境中，需要构建前端应用以生成静态文件：

```bash
npm run build
```

构建完成后，静态文件将生成在 `dist` 目录下。

### 4.4 启动前端应用

在开发环境中，可以使用开发服务器启动：

```bash
cd frontend && npm run dev
```

在生产环境中，通常不需要单独启动前端应用，而是通过 Web 服务器（如 Nginx）来托管构建后的静态文件 (`dist` 目录)。

## 5. 后期运维启动步骤 (待补充)

-   使用 PM2 管理后台 Node.js 进程。
-   配置 Nginx 作为反向代理，处理前端静态文件托管和后台 API 请求转发。
-   设置日志监控和轮转。
-   配置数据库备份策略。
-   考虑使用 Docker 进行容器化部署。

---

**管理员账号信息**:

默认管理员账号：`admin`
默认管理员密码：`admin123`

请在首次登录后修改默认密码。