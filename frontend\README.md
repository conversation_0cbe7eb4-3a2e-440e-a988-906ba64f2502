## 环境配置 (Environment Configuration)

本项目前端使用 [Vite](https://vitejs.dev/)，并遵循其标准的环境变量加载规则。

### 配置文件

*   `.env.development`: 用于本地开发环境 (`npm run dev`)。
*   `.env.production`: 用于生产环境构建 (`npm run build`)。
*   `.env.local`: 用于本地覆盖。你可以创建此文件来覆盖任意环境的配置，它已被 git 忽略。

**重要**: 只有以 `VITE_` 开头的环境变量才会暴露给客户端代码。

### 本地开发 (Local Development)

```bash
npm install
npm run dev
```
服务启动后，将默认连接到 `.env.development` 中定义的 `VITE_API_BASE_URL`。

### 生产构建 (Production Build)

```bash
npm run build
```
构建时，将使用 `.env.production` 中的配置。