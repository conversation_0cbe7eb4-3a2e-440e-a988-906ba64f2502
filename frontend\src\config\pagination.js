/**
 * 分页配置统一管理
 * 所有页面的分页配置都在这里定义，确保一致性
 */

// 基础分页配置
export const PAGINATION_CONFIG = {
  // 表格类页面（患者列表、医生列表、记录列表等）
  TABLE: {
    DEFAULT_PAGE_SIZE: 20,
    PAGE_SIZES: [10, 20, 50, 100],
    LAYOUT: 'total, sizes, prev, pager, next, jumper'
  },
  
  // 网格/卡片类页面（图片管理、文件管理等）
  GRID: {
    DEFAULT_PAGE_SIZE: 24,
    PAGE_SIZES: [12, 24, 48, 96],
    LAYOUT: 'total, sizes, prev, pager, next, jumper'
  },
  
  // 小组件/选择器（下拉选择、弹窗列表等）
  WIDGET: {
    DEFAULT_PAGE_SIZE: 10,
    PAGE_SIZES: [5, 10, 20, 50],
    LAYOUT: 'prev, pager, next'
  }
};

// 页面特定配置
export const PAGE_PAGINATION_CONFIG = {
  // 患者管理页面
  PATIENT_LIST: {
    type: 'TABLE',
    pageSize: 20,
    pageSizes: [10, 20, 50, 100]
  },
  
  // 医生管理页面
  DOCTOR_LIST: {
    type: 'TABLE',
    pageSize: 20,
    pageSizes: [10, 20, 50, 100]
  },
  
  // 图片管理页面
  IMAGE_LIST: {
    type: 'GRID',
    pageSize: 24,
    pageSizes: [12, 24, 48, 96]
  },
  
  // 病历记录页面
  MEDICAL_RECORD_LIST: {
    type: 'TABLE',
    pageSize: 15,
    pageSizes: [10, 15, 30, 50]
  },
  
  // 症状记录页面
  SYMPTOM_RECORD_LIST: {
    type: 'TABLE',
    pageSize: 20,
    pageSizes: [10, 20, 50, 100]
  }
};

/**
 * 获取页面分页配置
 * @param {string} pageName - 页面名称
 * @returns {Object} 分页配置
 */
export function getPagePaginationConfig(pageName) {
  const config = PAGE_PAGINATION_CONFIG[pageName];
  if (!config) {
    console.warn(`未找到页面 ${pageName} 的分页配置，使用默认配置`);
    return {
      page: 1,
      pageSize: PAGINATION_CONFIG.TABLE.DEFAULT_PAGE_SIZE,
      total: 0,
      pageSizes: PAGINATION_CONFIG.TABLE.PAGE_SIZES
    };
  }
  
  const baseConfig = PAGINATION_CONFIG[config.type];
  return {
    page: 1,
    pageSize: config.pageSize || baseConfig.DEFAULT_PAGE_SIZE,
    total: 0,
    pageSizes: config.pageSizes || baseConfig.PAGE_SIZES,
    layout: baseConfig.LAYOUT
  };
}

/**
 * 创建响应式分页对象
 * @param {string} pageName - 页面名称
 * @returns {import('vue').Ref} 响应式分页对象
 */
export function createPagePagination(pageName) {
  const { ref } = require('vue');
  return ref(getPagePaginationConfig(pageName));
}

// 导出常用的分页配置
export const COMMON_PAGINATION = {
  // 标准表格分页
  TABLE_20: {
    page: 1,
    pageSize: 20,
    total: 0,
    pageSizes: [10, 20, 50, 100]
  },
  
  // 标准网格分页
  GRID_24: {
    page: 1,
    pageSize: 24,
    total: 0,
    pageSizes: [12, 24, 48, 96]
  },
  
  // 小列表分页
  SMALL_10: {
    page: 1,
    pageSize: 10,
    total: 0,
    pageSizes: [5, 10, 20, 50]
  }
};

/**
 * 分页配置说明：
 * 
 * 1. 表格类页面（TABLE）：
 *    - 默认每页20条
 *    - 选项：[10, 20, 50, 100]
 *    - 适用：患者列表、医生列表、各种记录列表
 * 
 * 2. 网格类页面（GRID）：
 *    - 默认每页24条（4x6或6x4网格）
 *    - 选项：[12, 24, 48, 96]
 *    - 适用：图片管理、文件管理、卡片展示
 * 
 * 3. 小组件（WIDGET）：
 *    - 默认每页10条
 *    - 选项：[5, 10, 20, 50]
 *    - 适用：下拉选择器、弹窗列表
 * 
 * 使用方式：
 * 
 * // 方式1：使用页面特定配置
 * import { createPagePagination } from '@/config/pagination';
 * const pagination = createPagePagination('PATIENT_LIST');
 * 
 * // 方式2：使用通用配置
 * import { COMMON_PAGINATION } from '@/config/pagination';
 * const pagination = ref({ ...COMMON_PAGINATION.TABLE_20 });
 * 
 * // 方式3：使用配置函数
 * import { getPagePaginationConfig } from '@/config/pagination';
 * const pagination = ref(getPagePaginationConfig('IMAGE_LIST'));
 */
