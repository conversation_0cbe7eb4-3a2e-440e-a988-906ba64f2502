/**
 * 分页配置工具
 * 提供统一的分页配置和工具函数
 */

// 默认分页配置
export const DEFAULT_PAGINATION_CONFIG = {
  // 通用分页配置
  DEFAULT_PAGE_SIZE: 20,
  DEFAULT_PAGE_SIZES: [10, 20, 50, 100],
  
  // 表格类页面分页配置（如患者列表、医生列表等）
  TABLE_PAGE_SIZE: 20,
  TABLE_PAGE_SIZES: [10, 20, 50, 100],
  
  // 卡片/网格类页面分页配置（如图片管理等）
  GRID_PAGE_SIZE: 24,
  GRID_PAGE_SIZES: [12, 24, 48, 96],
  
  // 小组件分页配置（如下拉选择器等）
  WIDGET_PAGE_SIZE: 10,
  WIDGET_PAGE_SIZES: [5, 10, 20, 50]
};

/**
 * 创建分页配置对象
 * @param {string} type - 分页类型: 'table', 'grid', 'widget', 'default'
 * @param {Object} options - 自定义选项
 * @returns {Object} 分页配置对象
 */
export function createPaginationConfig(type = 'default', options = {}) {
  let config;
  
  switch (type) {
    case 'table':
      config = {
        page: 1,
        pageSize: DEFAULT_PAGINATION_CONFIG.TABLE_PAGE_SIZE,
        total: 0,
        pageSizes: DEFAULT_PAGINATION_CONFIG.TABLE_PAGE_SIZES
      };
      break;
      
    case 'grid':
      config = {
        page: 1,
        pageSize: DEFAULT_PAGINATION_CONFIG.GRID_PAGE_SIZE,
        total: 0,
        pageSizes: DEFAULT_PAGINATION_CONFIG.GRID_PAGE_SIZES
      };
      break;
      
    case 'widget':
      config = {
        page: 1,
        pageSize: DEFAULT_PAGINATION_CONFIG.WIDGET_PAGE_SIZE,
        total: 0,
        pageSizes: DEFAULT_PAGINATION_CONFIG.WIDGET_PAGE_SIZES
      };
      break;
      
    default:
      config = {
        page: 1,
        pageSize: DEFAULT_PAGINATION_CONFIG.DEFAULT_PAGE_SIZE,
        total: 0,
        pageSizes: DEFAULT_PAGINATION_CONFIG.DEFAULT_PAGE_SIZES
      };
  }
  
  // 合并自定义选项
  return { ...config, ...options };
}

/**
 * 创建分页响应式对象
 * @param {string} type - 分页类型
 * @param {Object} options - 自定义选项
 * @returns {import('vue').Ref} 响应式分页对象
 */
export function usePagination(type = 'default', options = {}) {
  const { ref } = require('vue');
  return ref(createPaginationConfig(type, options));
}

/**
 * 分页工具函数
 */
export const paginationUtils = {
  /**
   * 计算总页数
   * @param {number} total - 总记录数
   * @param {number} pageSize - 每页大小
   * @returns {number} 总页数
   */
  getTotalPages(total, pageSize) {
    return Math.ceil(total / pageSize);
  },
  
  /**
   * 计算当前页的记录范围
   * @param {number} page - 当前页
   * @param {number} pageSize - 每页大小
   * @returns {Object} { start, end }
   */
  getPageRange(page, pageSize) {
    const start = (page - 1) * pageSize + 1;
    const end = page * pageSize;
    return { start, end };
  },
  
  /**
   * 计算序号（用于表格行号）
   * @param {number} index - 当前行索引
   * @param {number} page - 当前页
   * @param {number} pageSize - 每页大小
   * @returns {number} 序号
   */
  getRowNumber(index, page, pageSize) {
    return (page - 1) * pageSize + index + 1;
  },
  
  /**
   * 验证页码是否有效
   * @param {number} page - 页码
   * @param {number} total - 总记录数
   * @param {number} pageSize - 每页大小
   * @returns {boolean} 是否有效
   */
  isValidPage(page, total, pageSize) {
    const totalPages = this.getTotalPages(total, pageSize);
    return page >= 1 && page <= totalPages;
  },
  
  /**
   * 修正页码（确保在有效范围内）
   * @param {number} page - 页码
   * @param {number} total - 总记录数
   * @param {number} pageSize - 每页大小
   * @returns {number} 修正后的页码
   */
  fixPage(page, total, pageSize) {
    const totalPages = this.getTotalPages(total, pageSize);
    if (page < 1) return 1;
    if (page > totalPages) return totalPages || 1;
    return page;
  }
};

/**
 * 分页事件处理器工厂
 * @param {Function} fetchData - 数据获取函数
 * @param {import('vue').Ref} pagination - 分页响应式对象
 * @returns {Object} 事件处理器对象
 */
export function createPaginationHandlers(fetchData, pagination) {
  return {
    /**
     * 处理页码变化
     * @param {number} newPage - 新页码
     */
    handleCurrentChange(newPage) {
      pagination.value.page = newPage;
      fetchData();
    },
    
    /**
     * 处理每页大小变化
     * @param {number} newSize - 新的每页大小
     */
    handleSizeChange(newSize) {
      pagination.value.pageSize = newSize;
      pagination.value.page = 1; // 重置到第一页
      fetchData();
    },
    
    /**
     * 重置分页到第一页
     */
    resetPagination() {
      pagination.value.page = 1;
    }
  };
}

/**
 * 使用示例：
 * 
 * // 在组件中使用
 * import { createPaginationConfig, createPaginationHandlers } from '@/utils/pagination';
 * 
 * export default {
 *   setup() {
 *     // 创建表格类型的分页配置
 *     const pagination = ref(createPaginationConfig('table'));
 *     
 *     // 或者创建网格类型的分页配置
 *     const gridPagination = ref(createPaginationConfig('grid'));
 *     
 *     // 创建分页事件处理器
 *     const { handleCurrentChange, handleSizeChange } = createPaginationHandlers(fetchData, pagination);
 *     
 *     return {
 *       pagination,
 *       handleCurrentChange,
 *       handleSizeChange
 *     };
 *   }
 * }
 */
