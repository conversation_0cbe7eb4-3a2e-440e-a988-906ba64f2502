const jwt = require('jsonwebtoken');

function authenticateToken(req, res, next) {
  console.log('=== 认证中间件 ===');
  console.log('请求URL:', req.url);
  console.log('请求方法:', req.method);
  console.log('请求头:', req.headers);

  const authHeader = req.headers['authorization'];
  console.log('Authorization 头:', authHeader);

  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
  console.log('提取的 token:', token ? token.substring(0, 20) + '...' : 'null');

  if (token == null) {
    console.log('认证失败: 未提供令牌');
    return res.status(401).json({ message: '未提供认证令牌。' });
  }

  const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_key_here_please_change_in_production';
  console.log('认证时使用的 JWT_SECRET:', jwtSecret);

  jwt.verify(token, jwtSecret, (err, user) => {
    if (err) {
      console.log('JWT 验证失败:', err.message);
      return res.status(403).json({ message: '令牌无效或已过期。' });
    }
    console.log('JWT 验证成功，用户信息:', user);
    req.user = user; // 将解码后的用户信息存储在 req.user 中
    next();
  });
}

module.exports = {
  authenticateToken
};