<template>
  <ContentContainer 
    title="系统状态"
    description="查看系统运行状态和各组件健康情况"
    :show-header="true"
  >
    <!-- 系统概览 -->
    <div class="status-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-item">
              <div class="status-icon" :class="backendStatus.class">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="status-info">
                <h3>后端服务</h3>
                <p>{{ backendStatus.text }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-item">
              <div class="status-icon" :class="databaseStatus.class">
                <el-icon><DataLine /></el-icon>
              </div>
              <div class="status-info">
                <h3>数据库</h3>
                <p>{{ databaseStatus.text }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-item">
              <div class="status-icon" :class="frontendStatus.class">
                <el-icon><Monitor /></el-icon>
              </div>
              <div class="status-info">
                <h3>前端服务</h3>
                <p>{{ frontendStatus.text }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-item">
              <div class="status-icon" :class="overallStatus.class">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="status-info">
                <h3>整体状态</h3>
                <p>{{ overallStatus.text }}</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细信息 -->
    <div class="status-details">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>系统信息</span>
                <el-button type="text" @click="refreshSystemInfo" :loading="loading">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </template>
            
            <el-descriptions :column="1" border>
              <el-descriptions-item label="后端版本">{{ systemInfo.version }}</el-descriptions-item>
              <el-descriptions-item label="运行环境">{{ systemInfo.environment }}</el-descriptions-item>
              <el-descriptions-item label="运行时间">{{ systemInfo.uptime }}</el-descriptions-item>
              <el-descriptions-item label="Node.js版本">{{ systemInfo.nodeVersion }}</el-descriptions-item>
              <el-descriptions-item label="启动时间">{{ systemInfo.startTime }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>性能指标</span>
                <el-button type="text" @click="refreshPerformance" :loading="loading">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </template>
            
            <el-descriptions :column="1" border>
              <el-descriptions-item label="内存使用">{{ performanceInfo.memory }}</el-descriptions-item>
              <el-descriptions-item label="活跃连接">{{ performanceInfo.activeConnections }}</el-descriptions-item>
              <el-descriptions-item label="响应时间">{{ performanceInfo.responseTime }}</el-descriptions-item>
              <el-descriptions-item label="错误率">{{ performanceInfo.errorRate }}</el-descriptions-item>
              <el-descriptions-item label="最后更新">{{ performanceInfo.lastUpdate }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 组件测试 -->
    <div class="component-test">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>组件测试</span>
            <el-button type="primary" @click="runAllTests" :loading="testLoading">
              <el-icon><VideoPlay /></el-icon>
              运行所有测试
            </el-button>
          </div>
        </template>
        
        <el-table :data="testResults" style="width: 100%">
          <el-table-column prop="name" label="测试项目" width="200" />
          <el-table-column prop="status" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'success' ? 'success' : scope.row.status === 'error' ? 'danger' : 'warning'">
                {{ scope.row.statusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="结果" />
          <el-table-column prop="duration" label="耗时" width="100" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="text" size="small" @click="runSingleTest(scope.row)">
                重新测试
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </ContentContainer>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Monitor, DataLine, CircleCheck, Refresh, VideoPlay } from '@element-plus/icons-vue'
import ContentContainer from '../components/ContentContainer.vue'
import api from '../api'

export default defineComponent({
  name: 'SystemStatus',
  components: {
    ContentContainer,
    Monitor,
    DataLine,
    CircleCheck,
    Refresh,
    VideoPlay
  },
  setup() {
    const loading = ref(false)
    const testLoading = ref(false)
    
    // 系统状态
    const systemInfo = ref({
      version: '1.0.0',
      environment: 'development',
      uptime: '0s',
      nodeVersion: 'N/A',
      startTime: 'N/A'
    })
    
    const performanceInfo = ref({
      memory: 'N/A',
      activeConnections: 0,
      responseTime: 'N/A',
      errorRate: '0%',
      lastUpdate: 'N/A'
    })
    
    // 服务状态
    const backendStatus = ref({ text: '检查中...', class: 'status-warning' })
    const databaseStatus = ref({ text: '检查中...', class: 'status-warning' })
    const frontendStatus = ref({ text: '正常', class: 'status-success' })
    
    // 整体状态
    const overallStatus = computed(() => {
      if (backendStatus.value.class === 'status-success' && 
          (databaseStatus.value.class === 'status-success' || databaseStatus.value.class === 'status-warning')) {
        return { text: '正常', class: 'status-success' }
      } else if (backendStatus.value.class === 'status-error') {
        return { text: '异常', class: 'status-error' }
      } else {
        return { text: '部分异常', class: 'status-warning' }
      }
    })
    
    // 测试结果
    const testResults = ref([
      { name: '后端API连接', status: 'pending', statusText: '待测试', message: '', duration: '' },
      { name: '健康检查端点', status: 'pending', statusText: '待测试', message: '', duration: '' },
      { name: '监控端点', status: 'pending', statusText: '待测试', message: '', duration: '' },
      { name: '认证功能', status: 'pending', statusText: '待测试', message: '', duration: '' }
    ])
    
    // 检查后端状态
    const checkBackendStatus = async () => {
      try {
        const startTime = Date.now()
        const response = await fetch('http://localhost:3000/')
        const duration = Date.now() - startTime
        
        if (response.ok) {
          backendStatus.value = { text: '正常', class: 'status-success' }
          performanceInfo.value.responseTime = `${duration}ms`
        } else {
          backendStatus.value = { text: '异常', class: 'status-error' }
        }
      } catch (error) {
        backendStatus.value = { text: '离线', class: 'status-error' }
      }
    }
    
    // 检查数据库状态
    const checkDatabaseStatus = async () => {
      try {
        const response = await fetch('http://localhost:3000/health')
        const data = await response.json()
        
        if (data.database === 'connected') {
          databaseStatus.value = { text: '已连接', class: 'status-success' }
        } else {
          databaseStatus.value = { text: '未连接', class: 'status-warning' }
        }
      } catch (error) {
        databaseStatus.value = { text: '检查失败', class: 'status-warning' }
      }
    }
    
    // 获取系统信息
    const refreshSystemInfo = async () => {
      loading.value = true
      try {
        const response = await fetch('http://localhost:3000/monitoring/info')
        const data = await response.json()
        
        systemInfo.value = {
          version: data.version || '1.0.0',
          environment: data.environment || 'development',
          uptime: formatUptime(data.uptime || 0),
          nodeVersion: data.nodeVersion || 'N/A',
          startTime: data.startTime || 'N/A'
        }
      } catch (error) {
        ElMessage.error('获取系统信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 获取性能信息
    const refreshPerformance = async () => {
      loading.value = true
      try {
        const response = await fetch('http://localhost:3000/monitoring/status')
        const data = await response.json()
        
        performanceInfo.value = {
          memory: formatMemory(data.memory?.heapUsed || 0),
          activeConnections: data.activeConnections || 0,
          responseTime: performanceInfo.value.responseTime,
          errorRate: '0%',
          lastUpdate: new Date().toLocaleTimeString()
        }
      } catch (error) {
        ElMessage.error('获取性能信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 运行单个测试
    const runSingleTest = async (test) => {
      const index = testResults.value.findIndex(t => t.name === test.name)
      if (index === -1) return
      
      testResults.value[index].status = 'pending'
      testResults.value[index].statusText = '测试中...'
      
      const startTime = Date.now()
      
      try {
        let result
        switch (test.name) {
          case '后端API连接':
            result = await testBackendAPI()
            break
          case '健康检查端点':
            result = await testHealthEndpoint()
            break
          case '监控端点':
            result = await testMonitoringEndpoint()
            break
          case '认证功能':
            result = await testAuthFunction()
            break
          default:
            result = { success: false, message: '未知测试项目' }
        }
        
        const duration = Date.now() - startTime
        testResults.value[index] = {
          ...testResults.value[index],
          status: result.success ? 'success' : 'error',
          statusText: result.success ? '通过' : '失败',
          message: result.message,
          duration: `${duration}ms`
        }
      } catch (error) {
        const duration = Date.now() - startTime
        testResults.value[index] = {
          ...testResults.value[index],
          status: 'error',
          statusText: '失败',
          message: error.message,
          duration: `${duration}ms`
        }
      }
    }
    
    // 运行所有测试
    const runAllTests = async () => {
      testLoading.value = true
      for (const test of testResults.value) {
        await runSingleTest(test)
      }
      testLoading.value = false
      ElMessage.success('所有测试完成')
    }
    
    // 测试函数
    const testBackendAPI = async () => {
      const response = await fetch('http://localhost:3000/')
      if (response.ok) {
        return { success: true, message: '后端API响应正常' }
      } else {
        return { success: false, message: `HTTP ${response.status}` }
      }
    }
    
    const testHealthEndpoint = async () => {
      const response = await fetch('http://localhost:3000/health')
      if (response.ok) {
        const data = await response.json()
        return { success: true, message: `状态: ${data.status}` }
      } else {
        return { success: false, message: `HTTP ${response.status}` }
      }
    }
    
    const testMonitoringEndpoint = async () => {
      const response = await fetch('http://localhost:3000/monitoring/status')
      if (response.ok) {
        return { success: true, message: '监控端点正常' }
      } else {
        return { success: false, message: `HTTP ${response.status}` }
      }
    }
    
    const testAuthFunction = async () => {
      // 简单的认证测试
      return { success: true, message: '认证功能可用（需要数据库）' }
    }
    
    // 工具函数
    const formatUptime = (seconds) => {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = Math.floor(seconds % 60)
      return `${hours}h ${minutes}m ${secs}s`
    }
    
    const formatMemory = (bytes) => {
      const mb = bytes / 1024 / 1024
      return `${mb.toFixed(2)} MB`
    }
    
    // 初始化
    onMounted(async () => {
      await checkBackendStatus()
      await checkDatabaseStatus()
      await refreshSystemInfo()
      await refreshPerformance()
    })
    
    return {
      loading,
      testLoading,
      systemInfo,
      performanceInfo,
      backendStatus,
      databaseStatus,
      frontendStatus,
      overallStatus,
      testResults,
      refreshSystemInfo,
      refreshPerformance,
      runSingleTest,
      runAllTests
    }
  }
})
</script>

<style scoped>
.status-overview {
  margin-bottom: var(--spacing-lg);
}

.status-card {
  height: 120px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-md);
  font-size: 24px;
  color: white;
}

.status-success {
  background: var(--success-color);
}

.status-warning {
  background: var(--warning-color);
}

.status-error {
  background: var(--error-color);
}

.status-info h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-md);
  color: var(--text-primary);
}

.status-info p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.status-details {
  margin-bottom: var(--spacing-lg);
}

.component-test {
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
