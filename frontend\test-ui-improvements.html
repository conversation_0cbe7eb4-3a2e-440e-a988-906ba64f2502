<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI改进测试页面</title>
    <style>
        /* 导入设计系统样式 */
        :root {
            /* 主色调 */
            --primary-color: #1890ff;
            --primary-light: #40a9ff;
            --primary-dark: #096dd9;
            --primary-hover: #40a9ff;
            
            /* 辅助色 */
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #ff4d4f;
            --info-color: #1890ff;
            
            /* 中性色 */
            --text-primary: #262626;
            --text-secondary: #595959;
            --text-tertiary: #8c8c8c;
            --text-disabled: #bfbfbf;
            
            /* 背景色 */
            --bg-primary: #ffffff;
            --bg-secondary: #fafafa;
            --bg-tertiary: #f5f5f5;
            --bg-disabled: #f5f5f5;
            
            /* 边框色 */
            --border-primary: #d9d9d9;
            --border-secondary: #f0f0f0;
            --border-light: #f5f5f5;
            
            /* 阴影 */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
            
            /* 间距 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            --spacing-xxl: 48px;
            
            /* 字体大小 */
            --font-size-xs: 12px;
            --font-size-sm: 14px;
            --font-size-md: 16px;
            --font-size-lg: 18px;
            --font-size-xl: 20px;
            --font-size-xxl: 24px;
            
            /* 字体粗细 */
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
            
            /* 行高 */
            --line-height-sm: 1.2;
            --line-height-md: 1.5;
            --line-height-lg: 1.8;
            
            /* 圆角 */
            --radius-sm: 4px;
            --radius-md: 6px;
            --radius-lg: 8px;
            --radius-xl: 12px;
            
            /* 过渡 */
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
            --transition-slow: 0.5s ease;
            
            /* Z-index层级 */
            --z-dropdown: 1000;
            --z-modal: 1050;
            --z-popover: 1060;
            --z-tooltip: 1070;
            --z-content: 1;
            --z-list-container: 10;
            --z-grid-container: 20;
        }

        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-size: var(--font-size-sm);
            color: var(--text-primary);
            background-color: var(--bg-secondary);
            line-height: var(--line-height-md);
        }

        /* 标准化列表控件样式 */
        .standard-breadcrumb-container {
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-light);
            margin-bottom: var(--spacing-md);
        }

        .standard-page-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
            border-bottom: 1px solid var(--border-secondary);
            background: var(--bg-tertiary);
            margin-bottom: var(--spacing-md);
        }

        .standard-page-title {
            font-size: var(--font-size-xxl);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0 0 var(--spacing-xs) 0;
            line-height: var(--line-height-sm);
        }

        .standard-page-description {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin: 0;
            line-height: var(--line-height-md);
        }

        .standard-list-container {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
            position: relative;
            z-index: var(--z-list-container);
            margin-bottom: var(--spacing-lg);
        }

        .standard-content-area {
            position: relative;
            z-index: var(--z-content);
            overflow: visible;
        }

        .standard-scroll-container {
            max-height: calc(100vh - 300px);
            overflow-y: auto;
            overflow-x: hidden;
            padding-bottom: var(--spacing-lg);
            position: relative;
            z-index: var(--z-grid-container);
        }

        .standard-grid-layout {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
            position: relative;
            z-index: var(--z-grid-container);
        }

        .standard-toolbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-md) var(--spacing-lg);
            background: var(--bg-tertiary);
            border-bottom: 1px solid var(--border-secondary);
        }

        .standard-toolbar-title {
            font-size: var(--font-size-md);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
        }

        .standard-toolbar-actions {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        /* 测试卡片样式 */
        .test-card {
            background: var(--bg-primary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-normal);
        }

        .test-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .test-grid-item {
            background: var(--bg-primary);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
            transition: var(--transition-normal);
        }

        .test-grid-item:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .demo-section {
            margin-bottom: var(--spacing-xxl);
        }

        .demo-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
        }

        .status-indicator {
            display: inline-block;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
        }

        .status-success {
            background: rgba(82, 196, 26, 0.1);
            color: var(--success-color);
        }

        .status-warning {
            background: rgba(250, 173, 20, 0.1);
            color: var(--warning-color);
        }

        .status-error {
            background: rgba(255, 77, 79, 0.1);
            color: var(--error-color);
        }
    </style>
</head>
<body>
    <h1>UI一致性改进测试页面</h1>
    
    <div class="demo-section">
        <h2 class="demo-title">改进状态总览</h2>
        <div class="test-card">
            <h3>✅ 已完成的改进</h3>
            <ul>
                <li><span class="status-indicator status-success">完成</span> 创建标准化样式系统</li>
                <li><span class="status-indicator status-success">完成</span> 修复图片网格z-index问题</li>
                <li><span class="status-indicator status-success">完成</span> 统一列表滚动行为</li>
                <li><span class="status-indicator status-success">完成</span> 应用标准化样式到现有模块</li>
            </ul>
        </div>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">标准化面包屑容器</h2>
        <div class="standard-breadcrumb-container">
            <span>首页 / 患者管理 / 患者列表</span>
        </div>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">标准化页面头部</h2>
        <div class="standard-page-header">
            <div class="header-left">
                <h1 class="standard-page-title">患者管理</h1>
                <p class="standard-page-description">管理和查看患者信息，包括基本信息、病史和症状记录</p>
            </div>
            <div class="header-right">
                <button>操作按钮</button>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">标准化列表容器 - 网格布局</h2>
        <div class="standard-list-container">
            <div class="standard-toolbar">
                <h3 class="standard-toolbar-title">图片网格视图</h3>
                <div class="standard-toolbar-actions">
                    <button>网格视图</button>
                    <button>列表视图</button>
                    <button>上传图片</button>
                </div>
            </div>
            <div class="standard-content-area">
                <div class="standard-scroll-container">
                    <div class="standard-grid-layout">
                        <div class="test-grid-item">图片卡片 1</div>
                        <div class="test-grid-item">图片卡片 2</div>
                        <div class="test-grid-item">图片卡片 3</div>
                        <div class="test-grid-item">图片卡片 4</div>
                        <div class="test-grid-item">图片卡片 5</div>
                        <div class="test-grid-item">图片卡片 6</div>
                        <div class="test-grid-item">图片卡片 7</div>
                        <div class="test-grid-item">图片卡片 8</div>
                        <div class="test-grid-item">图片卡片 9</div>
                        <div class="test-grid-item">图片卡片 10</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">Z-index层级测试</h2>
        <div class="test-card">
            <p><strong>层级设置：</strong></p>
            <ul>
                <li>content: z-index: 1</li>
                <li>list-container: z-index: 10</li>
                <li>grid-container: z-index: 20</li>
            </ul>
            <p>✅ 图片网格现在应该正确显示在页面内容之上</p>
        </div>
    </div>

    <script>
        // 测试滚动行为
        console.log('UI改进测试页面已加载');
        console.log('标准化样式系统已应用');
        
        // 检查滚动容器
        const scrollContainers = document.querySelectorAll('.standard-scroll-container');
        console.log(`找到 ${scrollContainers.length} 个滚动容器`);
        
        scrollContainers.forEach((container, index) => {
            console.log(`滚动容器 ${index + 1}:`, {
                scrollHeight: container.scrollHeight,
                clientHeight: container.clientHeight,
                canScroll: container.scrollHeight > container.clientHeight
            });
        });
    </script>
</body>
</html>
