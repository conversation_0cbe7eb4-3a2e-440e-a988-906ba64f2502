<template>
  <div class="data-table-container">
    <!-- 表格工具栏 -->
    <div v-if="showToolbar" class="standard-toolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <span class="standard-toolbar-title">{{ title }}</span>
        </slot>
      </div>
      <div class="standard-toolbar-actions">
        <slot name="toolbar-right">
          <!-- 默认工具按钮 -->
          <el-button
            v-if="showRefresh"
            type="text"
            @click="handleRefresh"
            icon="Refresh"
            title="刷新"
            size="small"
          />
          <el-button
            v-if="showColumnSetting"
            type="text"
            @click="showColumnDialog = true"
            icon="Setting"
            title="列设置"
            size="small"
          />
        </slot>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <div class="table-wrapper">
      <el-table
        ref="tableRef"
        :data="tableData"
        :loading="loading"
        :height="height"
        :max-height="maxHeight"
        :stripe="stripe"
        :border="border"
        :size="size"
        :empty-text="emptyText"
        :row-key="rowKey"
        :default-sort="defaultSort"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        @row-click="handleRowClick"
        @row-dblclick="handleRowDblClick"
        v-bind="$attrs"
      >
        <!-- 选择列 -->
        <el-table-column
          v-if="showSelection"
          type="selection"
          width="55"
          align="center"
          :selectable="selectable"
        />
        
        <!-- 序号列 -->
        <el-table-column
          v-if="showIndex"
          type="index"
          label="序号"
          width="60"
          align="center"
          :index="getIndex"
        />
        
        <!-- 动态列 -->
        <el-table-column
          v-for="column in visibleColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :align="column.align || 'left'"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
        >
          <template #default="scope">
            <!-- 自定义插槽 -->
            <slot 
              v-if="column.slot" 
              :name="column.slot" 
              :row="scope.row" 
              :column="column" 
              :$index="scope.$index"
            />
            
            <!-- 格式化显示 -->
            <span v-else-if="column.formatter">
              {{ column.formatter(scope.row, column, scope.row[column.prop], scope.$index) }}
            </span>
            
            <!-- 状态标签 -->
            <el-tag 
              v-else-if="column.type === 'status'"
              :type="getStatusType(scope.row[column.prop], column.statusMap)"
              size="small"
            >
              {{ getStatusText(scope.row[column.prop], column.statusMap) }}
            </el-tag>
            
            <!-- 默认显示 -->
            <span v-else>{{ scope.row[column.prop] }}</span>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column
          v-if="showActions"
          label="操作"
          :width="actionWidth"
          :fixed="actionFixed"
          align="center"
        >
          <template #default="scope">
            <slot 
              name="actions" 
              :row="scope.row" 
              :$index="scope.$index"
            >
              <!-- 默认操作按钮 -->
              <el-button 
                type="text" 
                size="small"
                @click="handleView(scope.row)"
              >
                查看
              </el-button>
              <el-button 
                type="text" 
                size="small"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button 
                type="text" 
                size="small"
                @click="handleDelete(scope.row)"
                class="text-error"
              >
                删除
              </el-button>
            </slot>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页组件 -->
    <div v-if="showPagination" class="standard-pagination-container">
      <StandardPagination
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="pageSizes"
        :layout="paginationLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 列设置对话框 -->
    <el-dialog
      v-model="showColumnDialog"
      title="列设置"
      width="400px"
    >
      <el-checkbox-group v-model="selectedColumns">
        <div v-for="column in allColumns" :key="column.prop" class="column-item">
          <el-checkbox :label="column.prop">{{ column.label }}</el-checkbox>
        </div>
      </el-checkbox-group>
      
      <template #footer>
        <el-button @click="showColumnDialog = false">取消</el-button>
        <el-button type="primary" @click="handleColumnSave">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue'
import StandardPagination from './StandardPagination.vue'

export default defineComponent({
  name: 'DataTable',
  components: {
    StandardPagination
  },
  props: {
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },
    // 列配置
    columns: {
      type: Array,
      required: true
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 分页配置
    pagination: {
      type: Object,
      default: () => ({
        page: 1,
        pageSize: 10,
        total: 0
      })
    },
    // 表格标题
    title: {
      type: String,
      default: ''
    },
    // 表格高度
    height: {
      type: [String, Number],
      default: undefined
    },
    // 最大高度
    maxHeight: {
      type: [String, Number],
      default: undefined
    },
    // 是否显示工具栏
    showToolbar: {
      type: Boolean,
      default: true
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 是否显示选择列
    showSelection: {
      type: Boolean,
      default: false
    },
    // 是否显示序号列
    showIndex: {
      type: Boolean,
      default: false
    },
    // 是否显示操作列
    showActions: {
      type: Boolean,
      default: true
    },
    // 操作列宽度
    actionWidth: {
      type: [String, Number],
      default: 180
    },
    // 操作列固定
    actionFixed: {
      type: String,
      default: 'right'
    },
    // 其他表格属性
    stripe: {
      type: Boolean,
      default: true
    },
    border: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'default'
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    rowKey: {
      type: String,
      default: 'id'
    },
    defaultSort: {
      type: Object,
      default: undefined
    },
    // 分页配置
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },
    paginationLayout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    // 工具栏配置
    showRefresh: {
      type: Boolean,
      default: true
    },
    showColumnSetting: {
      type: Boolean,
      default: true
    },
    // 选择函数
    selectable: {
      type: Function,
      default: undefined
    }
  },
  emits: [
    'selection-change',
    'sort-change', 
    'row-click',
    'row-dblclick',
    'size-change',
    'current-change',
    'refresh',
    'view',
    'edit',
    'delete'
  ],
  setup(props, { emit }) {
    const tableRef = ref()
    const showColumnDialog = ref(false)
    const selectedColumns = ref([])
    
    // 表格数据
    const tableData = computed(() => props.data)
    
    // 所有列
    const allColumns = computed(() => props.columns)
    
    // 可见列
    const visibleColumns = computed(() => {
      if (selectedColumns.value.length === 0) {
        return allColumns.value
      }
      return allColumns.value.filter(column => 
        selectedColumns.value.includes(column.prop)
      )
    })
    
    // 初始化选中的列
    watch(() => props.columns, (newColumns) => {
      if (selectedColumns.value.length === 0) {
        selectedColumns.value = newColumns.map(col => col.prop)
      }
    }, { immediate: true })
    
    // 获取序号
    const getIndex = (index) => {
      return (props.pagination.page - 1) * props.pagination.pageSize + index + 1
    }
    
    // 获取状态类型
    const getStatusType = (value, statusMap) => {
      if (!statusMap) return 'info'
      const status = statusMap[value]
      return status?.type || 'info'
    }
    
    // 获取状态文本
    const getStatusText = (value, statusMap) => {
      if (!statusMap) return value
      const status = statusMap[value]
      return status?.text || value
    }
    
    // 事件处理
    const handleSelectionChange = (selection) => {
      emit('selection-change', selection)
    }
    
    const handleSortChange = (sortInfo) => {
      emit('sort-change', sortInfo)
    }
    
    const handleRowClick = (row, column, event) => {
      emit('row-click', row, column, event)
    }
    
    const handleRowDblClick = (row, column, event) => {
      emit('row-dblclick', row, column, event)
    }
    
    const handleSizeChange = (size) => {
      emit('size-change', size)
    }
    
    const handleCurrentChange = (page) => {
      emit('current-change', page)
    }
    
    const handleRefresh = () => {
      emit('refresh')
    }
    
    const handleView = (row) => {
      emit('view', row)
    }
    
    const handleEdit = (row) => {
      emit('edit', row)
    }
    
    const handleDelete = (row) => {
      emit('delete', row)
    }
    
    const handleColumnSave = () => {
      showColumnDialog.value = false
    }
    
    return {
      tableRef,
      tableData,
      allColumns,
      visibleColumns,
      showColumnDialog,
      selectedColumns,
      getIndex,
      getStatusType,
      getStatusText,
      handleSelectionChange,
      handleSortChange,
      handleRowClick,
      handleRowDblClick,
      handleSizeChange,
      handleCurrentChange,
      handleRefresh,
      handleView,
      handleEdit,
      handleDelete,
      handleColumnSave
    }
  }
})
</script>

<style scoped>
.data-table-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

/* 使用标准化样式，移除重复定义 */

.table-wrapper {
  overflow: hidden;
}

.column-item {
  padding: var(--spacing-xs) 0;
}

:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  font-weight: var(--font-weight-semibold);
}

:deep(.el-table td) {
  border-bottom: 1px solid var(--border-light);
}

:deep(.el-table__empty-text) {
  color: var(--text-tertiary);
}

.text-error {
  color: var(--error-color) !important;
}
</style>
