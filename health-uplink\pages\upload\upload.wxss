/* pages/upload/upload.wxss */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  padding: 20px;
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.5s ease-out; /* 应用淡入动画 */
}

.type-selection-section {
  margin-top: 0; /* Handled by container padding */
  margin-top: 0; /* Removed fixed margin, padding is handled by container */
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.picker-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  color: #333;
}

.picker-label {
  color: #666;
  margin-right: 10px;
}

.selected-type {
  flex: 1;
  text-align: right;
  font-weight: bold;
}

.picker .arrow {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.image-upload-section {
  display: flex;
  justify-content: space-around; /* Distribute space around items */
  align-items: center;
  margin-bottom: 10px;
  width: 100%; /* Ensure the container takes full width */
}

.choose-image-button,
.batch-upload-button,
.clear-all-button {
  flex: 1; /* Allow buttons to grow and shrink */
  margin: 0 5px; /* Add some space between buttons */
  background-color: #1890ff;
  color: #fff;
  font-size: 16px;
  padding: 10px 0; /* Adjust padding for better fit */
  border-radius: 8px;
  transition: transform 0.2s ease-in-out;
  text-align: center; /* Center text in buttons */
}

.choose-image-button:active, /* 添加点击反馈样式 */
.batch-upload-button:active,
.clear-all-button:active {
  transform: translateY(2px);
}

.batch-upload-button {
  background-color: #52c41a; /* Different color for batch upload */
}

.clear-all-button {
  background-color: #ff4d4f; /* Red color for clear all */
}

.choose-image-button::after,
.batch-upload-button::after,
.clear-all-button::after { /* 移除默认边框 */
  border: none;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 20px;
  flex-grow: 1;
  overflow-y: auto;
}

.image-item {
  position: relative;
  width: 100%;
  padding-bottom: 100%; /* 1:1 aspect ratio */
  background-color: #e9e9e9;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.image-item image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 12px;
  padding: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-type {
  font-weight: bold;
}

.upload-status {
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 10px;
}

.status-success {
  background-color: #52c41a;
}

.status-pending {
  background-color: #faad14;
}

.status-uploading {
  background-color: #1890ff;
}

.progress-bar {
  width: 100%;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  margin-top: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #52c41a;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.delete-button {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 0, 0, 0.7);
  color: #fff;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
  z-index: 10;
}

.upload-button {
  width: 100%;
  background-color: #1890ff;
  color: #fff;
  border-radius: 8px;
  padding: 12px 0;
  font-size: 18px;
  margin-top: 10px;
  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);
  transition: transform 0.2s ease-in-out;
}

.upload-button:active { /* 添加点击反馈样式 */
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(24, 144, 255, 0.5);
}

.upload-button::after {
  border: none;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5px 0;
  color: #999;
  font-size: 16px;
  flex-grow: 1; /* Allow empty state to take up available space */
}

.empty-image {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
  opacity: 0.6;
}

.empty-text {
  font-size: 15px;
  color: #666;
}
