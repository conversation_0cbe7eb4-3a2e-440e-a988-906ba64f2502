<template>
  <ContentContainer
    title="图片管理"
    description="管理和查看患者上传的医疗影像资料"
    :show-header="true"
    :breadcrumbs="breadcrumbs"
    :show-breadcrumb="true"
  >
    <!-- 搜索表单 -->
    <SearchForm
      :search-fields="searchFields"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 图片管理组件 -->
    <ImageManager
      :images="images"
      :loading="loading"
      :pagination="pagination"
      :show-debug-info="true"
      @upload="handleUpload"
      @delete="handleDelete"
      @download="handleDownload"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      @refresh="fetchImages"
    >
      <!-- 自定义工具栏操作 -->
      <template #toolbar-actions>
        <el-button
          type="primary"
          @click="handleUpload"
          icon="Upload"
        >
          上传图片
        </el-button>
        <el-button
          type="info"
          @click="testImageService"
          icon="Connection"
        >
          测试图片服务
        </el-button>
        <el-button
          type="warning"
          @click="adjustGridScrolling"
          icon="Refresh"
        >
          检查滚动
        </el-button>
      </template>
    </ImageManager>
  </ContentContainer>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import api from '../api';
import { getPagePaginationConfig } from '../config/pagination';

export default defineComponent({
  name: 'ImageList',
  setup() {
    const images = ref([]);
    const loading = ref(false);
    const searchParams = ref({});

    // 分页配置 - 使用图片管理页面的分页配置
    const pagination = ref(getPagePaginationConfig('IMAGE_LIST'));

    // 面包屑导航
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '图片管理', path: '/images' }
    ]);

    // 搜索字段配置
    const searchFields = computed(() => [
      {
        key: 'patient_id',
        label: '患者ID',
        type: 'input',
        placeholder: '请输入患者ID',
        clearable: true
      },
      {
        key: 'filename',
        label: '文件名',
        type: 'input',
        placeholder: '请输入文件名',
        clearable: true
      },
      {
        key: 'date_range',
        label: '上传时间',
        type: 'daterange',
        placeholder: '选择时间范围',
        clearable: true
      }
    ]);

    // 获取图片列表
    const fetchImages = async () => {
      loading.value = true;
      try {
        const params = {
          page: pagination.value.page,
          limit: pagination.value.pageSize,
          ...searchParams.value
        };

        const response = await api.get('/image/list', { params });

        if (response.images) {
          images.value = response.images;
          pagination.value.total = response.total;
          console.log('成功加载图片:', images.value.length, '张');
        } else {
          images.value = [];
          pagination.value.total = 0;
          ElMessage.error(response.message || '获取图片列表失败');
        }
      } catch (error) {
        console.error('获取图片列表失败:', error);

        let errorMessage = '获取图片列表失败';
        if (error.message.includes('权限不足')) {
          errorMessage = '权限不足，请重新登录';
        }

        ElMessage.error(errorMessage);
        images.value = [];
        pagination.value.total = 0;
      } finally {
        loading.value = false;
      }
    };

    // 搜索处理
    const handleSearch = (searchData) => {
      searchParams.value = searchData;
      pagination.value.page = 1;
      fetchImages();
    };

    // 重置搜索
    const handleReset = () => {
      searchParams.value = {};
      pagination.value.page = 1;
      fetchImages();
    };

    // 分页处理
    const handleSizeChange = (newSize) => {
      pagination.value.pageSize = newSize;
      pagination.value.page = 1;
      fetchImages();
    };

    const handleCurrentChange = (newPage) => {
      pagination.value.page = newPage;
      fetchImages();
    };

    // 删除图片处理
    const handleDelete = async (image) => {
      try {
        const response = await api.delete(`/image/${image.image_id}`);
        if (response.success) {
          ElMessage.success('图片删除成功');
          fetchImages(); // 刷新列表
        } else {
          ElMessage.error(response.message || '删除图片失败');
        }
      } catch (error) {
        console.error('删除图片失败:', error);
        ElMessage.error('删除图片失败，请稍后重试');
      }
    };

    // 下载图片处理
    const handleDownload = (image) => {
      console.log('下载图片:', image.original_name);
      ElMessage.success(`开始下载: ${image.original_name}`);
    };

    // 上传图片
    const handleUpload = () => {
      ElMessage.info('上传功能开发中...');
    };

    // 测试图片服务
    const testImageService = async () => {
      try {
        const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';
        const debugResponse = await fetch(`${baseUrl}/api/debug/images`);
        const debugData = await debugResponse.json();

        console.log('图片服务调试信息:', debugData);
        ElMessage.success(`图片服务正常！找到 ${debugData.count} 个图片文件`);
      } catch (error) {
        console.error('图片服务测试失败:', error);
        ElMessage.error(`图片服务测试失败: ${error.message}`);
      }
    };

    // 调整网格滚动的方法
    const adjustGridScrolling = () => {
      console.log('检查滚动状态');
      ElMessage.info('滚动检查完成');
    };

    onMounted(() => {
      fetchImages();
    });

    return {
      images,
      loading,
      pagination,
      breadcrumbs,
      searchFields,
      fetchImages,
      handleSearch,
      handleReset,
      handleSizeChange,
      handleCurrentChange,
      handleDelete,
      handleDownload,
      handleUpload,
      testImageService,
      adjustGridScrolling
    };
  }
});
</script>

<style scoped>
/* 页面特定样式 */
</style>

