<template>
  <div class="debug-container">
    <h2>图片管理调试页面</h2>
    
    <div class="debug-section">
      <h3>环境信息</h3>
      <p>API Base URL: {{ apiBaseUrl }}</p>
      <p>Token: {{ token ? token.substring(0, 20) + '...' : '无' }}</p>
    </div>

    <div class="debug-section">
      <h3>API测试</h3>
      <button @click="testApi" :disabled="loading">测试API连接</button>
      <div v-if="apiResult" class="api-result">
        <h4>API响应:</h4>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
      <div v-if="apiError" class="api-error">
        <h4>API错误:</h4>
        <pre>{{ apiError }}</pre>
      </div>
    </div>

    <div class="debug-section">
      <h3>图片列表</h3>
      <button @click="fetchImages" :disabled="loading">获取图片列表</button>
      <div v-if="loading">加载中...</div>
      <div v-if="images.length > 0">
        <p>找到 {{ images.length }} 张图片</p>
        <div v-for="(image, index) in images" :key="image.image_id" class="image-item">
          <p>图片 {{ index + 1 }}: {{ image.original_name }}</p>
          <p>URL: {{ image.image_url }}</p>
          <p>完整URL: {{ getImageUrl(image.image_url) }}</p>
        </div>
      </div>
      <div v-else-if="!loading">
        <p>没有找到图片</p>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue';
import api from '../api';

export default defineComponent({
  name: 'ImageListDebug',
  setup() {
    const loading = ref(false);
    const images = ref([]);
    const apiResult = ref(null);
    const apiError = ref(null);
    const token = ref(localStorage.getItem('token'));
    const apiBaseUrl = ref(import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000');

    const testApi = async () => {
      loading.value = true;
      apiResult.value = null;
      apiError.value = null;
      
      try {
        const response = await fetch('http://localhost:3000/health');
        const data = await response.json();
        apiResult.value = data;
      } catch (error) {
        apiError.value = error.message;
      } finally {
        loading.value = false;
      }
    };

    const fetchImages = async () => {
      loading.value = true;
      apiResult.value = null;
      apiError.value = null;
      
      try {
        const response = await api.get('/image/list');
        images.value = response.images || [];
        apiResult.value = response;
      } catch (error) {
        apiError.value = error.message;
        console.error('获取图片列表失败:', error);
      } finally {
        loading.value = false;
      }
    };

    const getImageUrl = (url) => {
      if (!url) return '';
      if (url.startsWith('http')) return url;
      return `${apiBaseUrl.value}${url}`;
    };

    return {
      loading,
      images,
      apiResult,
      apiError,
      token,
      apiBaseUrl,
      testApi,
      fetchImages,
      getImageUrl
    };
  }
});
</script>

<style scoped>
.debug-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.debug-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.api-result, .api-error {
  margin-top: 10px;
  padding: 10px;
  border-radius: 3px;
}

.api-result {
  background-color: #f0f8ff;
  border: 1px solid #87ceeb;
}

.api-error {
  background-color: #ffe4e1;
  border: 1px solid #ff6b6b;
}

.image-item {
  margin: 10px 0;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 3px;
}

button {
  padding: 8px 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
