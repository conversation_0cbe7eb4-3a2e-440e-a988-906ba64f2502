const promClient = require('prom-client');
const responseTime = require('response-time');
const logger = require('../utils/logger');

// 创建一个注册表
const register = new promClient.Registry();

// 添加默认指标
promClient.collectDefaultMetrics({
  register,
  prefix: 'health_uplink_'
});

// 自定义指标
const httpRequestDuration = new promClient.Histogram({
  name: 'health_uplink_http_request_duration_seconds',
  help: 'HTTP request duration in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.5, 1, 2, 5]
});

const httpRequestTotal = new promClient.Counter({
  name: 'health_uplink_http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const activeConnections = new promClient.Gauge({
  name: 'health_uplink_active_connections',
  help: 'Number of active connections'
});

const databaseConnections = new promClient.Gauge({
  name: 'health_uplink_database_connections',
  help: 'Number of database connections',
  labelNames: ['state']
});

const errorRate = new promClient.Counter({
  name: 'health_uplink_errors_total',
  help: 'Total number of errors',
  labelNames: ['type', 'endpoint']
});

// 注册指标
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(activeConnections);
register.registerMetric(databaseConnections);
register.registerMetric(errorRate);

// 中间件：记录HTTP请求指标
const metricsMiddleware = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route ? req.route.path : req.path;
    const method = req.method;
    const statusCode = res.statusCode.toString();
    
    // 记录请求持续时间
    httpRequestDuration
      .labels(method, route, statusCode)
      .observe(duration);
    
    // 记录请求总数
    httpRequestTotal
      .labels(method, route, statusCode)
      .inc();
    
    // 记录错误
    if (statusCode.startsWith('4') || statusCode.startsWith('5')) {
      errorRate
        .labels('http_error', route)
        .inc();
    }
  });
  
  next();
};

// 响应时间中间件
const responseTimeMiddleware = responseTime((req, res, time) => {
  const route = req.route ? req.route.path : req.path;
  logger.info(`${req.method} ${route} - ${time.toFixed(2)}ms`);
});

// 错误追踪中间件
const errorTrackingMiddleware = (err, req, res, next) => {
  const route = req.route ? req.route.path : req.path;
  
  // 记录错误指标
  errorRate
    .labels('application_error', route)
    .inc();
  
  // 记录详细错误信息
  logger.error('Application Error:', {
    error: err.message,
    stack: err.stack,
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    timestamp: new Date().toISOString()
  });
  
  next(err);
};

// 更新活跃连接数
let connectionCount = 0;
const updateActiveConnections = (req, res, next) => {
  connectionCount++;
  activeConnections.set(connectionCount);
  
  res.on('finish', () => {
    connectionCount--;
    activeConnections.set(connectionCount);
  });
  
  next();
};

// 获取指标数据
const getMetrics = async () => {
  return await register.metrics();
};

// 获取系统状态
const getSystemStatus = () => {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  return {
    uptime: process.uptime(),
    memory: {
      rss: memUsage.rss,
      heapTotal: memUsage.heapTotal,
      heapUsed: memUsage.heapUsed,
      external: memUsage.external
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system
    },
    activeConnections: connectionCount,
    timestamp: new Date().toISOString()
  };
};

// 数据库连接监控
const updateDatabaseMetrics = (sequelize) => {
  if (sequelize && sequelize.connectionManager) {
    const pool = sequelize.connectionManager.pool;
    if (pool) {
      databaseConnections.labels('active').set(pool.size || 0);
      databaseConnections.labels('idle').set(pool.available || 0);
    }
  }
};

module.exports = {
  metricsMiddleware,
  responseTimeMiddleware,
  errorTrackingMiddleware,
  updateActiveConnections,
  getMetrics,
  getSystemStatus,
  updateDatabaseMetrics,
  register
};
