/**
 * 全局组件注册
 * 将常用的公共组件注册为全局组件，方便在各个页面中直接使用
 */

// 导入公共组件
import ContentContainer from './ContentContainer.vue';
import DataTable from './DataTable.vue';
import SearchForm from './SearchForm.vue';
import StandardForm from './StandardForm.vue';
import StandardPagination from './StandardPagination.vue';
import ImageManager from './ImageManager.vue';

/**
 * 注册全局组件
 * @param {Object} app - Vue应用实例
 */
export function registerGlobalComponents(app) {
  // 布局容器组件
  app.component('ContentContainer', ContentContainer);
  
  // 数据表格组件
  app.component('DataTable', DataTable);
  
  // 搜索表单组件
  app.component('SearchForm', SearchForm);
  
  // 标准表单组件
  app.component('StandardForm', StandardForm);
  
  // 标准分页组件
  app.component('StandardPagination', StandardPagination);
  
  // 图片管理组件
  app.component('ImageManager', ImageManager);
}

/**
 * 组件使用说明
 * 
 * 1. ContentContainer - 页面内容容器
 *    用法：<ContentContainer title="页面标题" description="页面描述">内容</ContentContainer>
 * 
 * 2. DataTable - 数据表格
 *    用法：<DataTable :data="tableData" :columns="columns" />
 * 
 * 3. SearchForm - 搜索表单
 *    用法：<SearchForm :search-fields="searchFields" @search="handleSearch" />
 * 
 * 4. StandardForm - 标准表单
 *    用法：<StandardForm :form-config="formConfig" @submit="handleSubmit" />
 * 
 * 5. StandardPagination - 标准分页
 *    用法：<StandardPagination 
 *            :current-page="page" 
 *            :page-size="pageSize" 
 *            :total="total"
 *            @current-change="handlePageChange"
 *            @size-change="handleSizeChange" />
 * 
 * 6. ImageManager - 图片管理组件
 *    用法：<ImageManager 
 *            :images="images"
 *            :loading="loading"
 *            :pagination="pagination"
 *            @upload="handleUpload"
 *            @delete="handleDelete"
 *            @current-change="handlePageChange" />
 */

export default {
  registerGlobalComponents
};
