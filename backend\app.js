const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const morgan = require('morgan'); // 引入 morgan
const logger = require('./utils/logger'); // 引入 logger
const rateLimit = require('express-rate-limit'); // 引入 express-rate-limit
const { sequelize, testConnection } = require('./config/config'); // 引入 Sequelize 实例和数据库连接测试函数
const models = require('./models'); // 引入所有模型
const {
  metricsMiddleware,
  responseTimeMiddleware,
  errorTrackingMiddleware,
  updateActiveConnections,
  updateDatabaseMetrics
} = require('./middlewares/monitoring'); // 引入监控中间件

// --- 环境变量加载逻辑 ---
const path = require('path');
const fs = require('fs');

// 确定环境
const NODE_ENV = process.env.NODE_ENV || 'development';

// 定义 .env 文件加载顺序
const envFiles = [
  `.env.${NODE_ENV}.local`, // 特定环境的本地覆盖文件
  `.env.${NODE_ENV}`,       // 特定环境的配置文件
  '.env.local',             // 全局本地覆盖文件
  '.env'                    // 全局默认文件 (如果存在)
];

console.log(`[ENV] 当前环境 (NODE_ENV): ${NODE_ENV}`);
console.log(`[ENV] 将按以下顺序加载 .env 文件:`, envFiles);

envFiles.forEach(file => {
  const filePath = path.resolve(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`[ENV] 正在加载: ${file}`);
    dotenv.config({
      path: filePath,
      override: true // 允许后加载的文件覆盖先加载的
    });
  }
});
// --- 环境变量加载逻辑结束 ---

const app = express();

// 配置信任代理（用于Docker环境中的nginx代理）
app.set('trust proxy', true);

// 配置 API 访问频率限制
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 分钟
  max: 100, // 在 15 分钟内，每个 IP 最多可以请求 100 次
  message: '请求过于频繁，请稍后再试。'
});

// 将频率限制中间件应用于所有 /api/ 路径，但排除调试路由
app.use('/api/', (req, res, next) => {
  // 跳过调试路由的频率限制
  if (req.path.startsWith('/debug/')) {
    return next();
  }
  return apiLimiter(req, res, next);
});

// 测试数据库连接（异步，不阻塞应用启动）
testConnection().catch(err => {
  console.warn('数据库连接失败，应用将在无数据库模式下运行:', err.message);
});

// 数据库同步配置
const dbSyncEnabled = process.env.DB_SYNC_ENABLED !== 'false'; // 默认启用同步
const forceSync = process.env.NODE_ENV === 'development' && process.env.DB_SYNC_FORCE === 'true';
const alterSync = process.env.DB_SYNC_ALTER === 'true'; // 是否启用alter模式

// 数据库同步（异步，不阻塞应用启动）
if (dbSyncEnabled) {
  console.log('开始数据库同步...');
  console.log(`同步模式: ${forceSync ? '强制同步(force)' : alterSync ? '结构更新(alter)' : '安全同步'}`);

  const syncOptions = {};
  if (forceSync) {
    // 强制同步：删除现有表并重新创建（仅开发环境）
    syncOptions.force = true;
    console.warn('⚠️  警告：使用强制同步模式，将删除所有现有数据！');
  } else if (alterSync) {
    // 结构更新：修改现有表结构以匹配模型（谨慎使用）
    syncOptions.alter = true;
    console.log('使用结构更新模式，将修改表结构以匹配模型定义');
  }

  sequelize.sync(syncOptions)
    .then(() => {
      console.log(`✅ 数据库同步完成。${forceSync ? '(强制同步)' : alterSync ? '(结构更新)' : '(安全同步)'}`);
      // 创建管理员的逻辑已移至独立的 seeder 脚本中
    })
    .catch(err => {
      console.error('❌ 数据库同步失败:', err.message);
      console.warn('应用将在无数据库模式下运行，某些功能可能不可用');
    });
} else {
  console.log('⏭️  数据库同步已禁用，跳过同步步骤');
  console.log('如需启用数据库同步，请设置环境变量 DB_SYNC_ENABLED=true');
}

// 中间件
// 配置CORS
const corsOptions = {
  origin: true, // 允许所有来源
  credentials: true, // 允许发送凭证
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
};
app.use(cors(corsOptions)); // 启用 CORS

// 增加请求体大小限制，支持文件上传
app.use(express.json({ limit: '10mb' })); // 解析 JSON 格式的请求体
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // 解析 URL-encoded 格式的请求体

// 静态文件服务 - 提供上传的图片访问
const uploadPath = path.join(__dirname, 'uploads/images');
console.log('静态文件服务路径:', uploadPath);
app.use('/uploads/images', express.static(uploadPath)); // 提供上传文件的静态访问



// 监控中间件
app.use(updateActiveConnections); // 活跃连接监控
app.use(metricsMiddleware); // 指标收集
app.use(responseTimeMiddleware); // 响应时间监控

// HTTP 请求日志
app.use(morgan('combined', { stream: logger.stream }));

// 引入路由
const patientRoutes = require('./routes/patient');
const imageRoutes = require('./routes/image'); // 引入图片路由
const exportRoutes = require('./routes/export'); // 引入导出路由
const userRoutes = require('./routes/user'); // 引入用户路由
const logRoutes = require('./routes/log'); // 引入日志路由
const monitoringRoutes = require('./routes/monitoring'); // 引入监控路由
const backupRoutes = require('./routes/backup'); // 引入备份路由

// 添加调试路由
app.get('/api/debug/images', (req, res) => {
  const fs = require('fs');
  const path = require('path');
  const uploadPath = path.join(__dirname, '../uploads/images');

  try {
    if (!fs.existsSync(uploadPath)) {
      return res.json({
        uploadPath,
        files: [],
        count: 0,
        message: '上传目录不存在'
      });
    }

    const files = fs.readdirSync(uploadPath);
    res.json({
      uploadPath,
      files,
      count: files.length,
      message: '调试API正常工作'
    });
  } catch (error) {
    console.error('调试API错误:', error);
    res.status(500).json({
      error: error.message,
      uploadPath,
      message: '调试API遇到错误'
    });
  }
});

// 使用路由
app.use('/api/patient', patientRoutes);
app.use('/api/image', imageRoutes); // 使用图片路由
app.use('/api/export', exportRoutes); // 使用导出路由
app.use('/api/user', userRoutes); // 使用用户路由
app.use('/api/log', logRoutes); // 使用日志路由
app.use('/monitoring', monitoringRoutes); // 使用监控路由
app.use('/api/backup', backupRoutes); // 使用备份路由

// 根路由
app.get('/', (req, res) => {
  res.send('Health Uplink Backend API is running!');
});

// 健康检查路由
app.get('/health', async (req, res) => {
  try {
    // 检查数据库连接
    await sequelize.authenticate();

    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: 'connected',
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    });
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// 错误处理中间件 (放在所有路由之后)
app.use(errorTrackingMiddleware); // 错误追踪中间件
app.use((err, req, res, next) => {
  logger.error(err.stack); // 记录错误堆栈
  res.status(500).send('Something broke!');
});

// 定期更新数据库指标
setInterval(() => {
  updateDatabaseMetrics(sequelize);
}, 30000); // 每30秒更新一次

// 端口监听
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
  logger.info(`Health Uplink Backend started on port ${PORT}`);
});

module.exports = app;