<template>
  <div class="login-container">
    <el-card class="login-card">
      <template #header>
        <div class="card-header">
          <span>健康信息收集系统管理后台</span>
        </div>
      </template>
      <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="loginForm.username"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input type="password" v-model="loginForm.password"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router';
import api from '../api'; // 引入封装的 api

export default defineComponent({
  setup() {
    const router = useRouter();
    const loginFormRef = ref(null);
    const loginForm = ref({
      username: '',
      password: ''
    });

    const loginRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
      ]
    };

    const submitForm = () => {
      loginFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            const response = await api.post('/user/login', loginForm.value);
            console.log('后端响应:', response); // 添加日志打印响应对象
            console.log('后端响应 token:', response.token); // 打印 token
            console.log('后端响应 doctor.name:', response.doctor ? response.doctor.name : 'N/A'); // 打印用户名
            if (response.token) { // 根据 token 字段判断登录是否成功
              localStorage.setItem('token', response.token); // 从 response 中获取 token
              localStorage.setItem('username', response.doctor.name); // 从 response 中获取用户名
              console.log('Token 已设置:', localStorage.getItem('token')); // 确认 token 已设置
              console.log('Username 已设置:', localStorage.getItem('username')); // 确认 username 已设置
              router.push('/'); // 登录成功跳转到首页
              console.log('已尝试路由跳转到 /'); // 记录路由跳转
            } else {
              // 登录失败处理
              console.error('登录失败:', response.message);
              alert(response.message || '登录失败'); // 使用 alert 简单提示
            }
          } catch (error) {
            console.error('登录请求失败:', error);
            if (error.response) {
              console.error('错误响应数据:', error.response.data);
              console.error('错误响应状态码:', error.response.status);
              console.error('错误响应头:', error.response.headers);
              alert(error.response.data.message || '登录请求失败，请稍后再试。');
            } else if (error.request) {
              console.error('错误请求:', error.request);
              alert('登录请求无响应，请检查网络或后端服务。');
            } else {
              console.error('错误信息:', error.message);
              alert('登录请求失败，请稍后再试。');
            }
          }
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    };

    return {
      loginFormRef,
      loginForm,
      loginRules,
      submitForm
    };
  }
});
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}

.login-card {
  width: 400px;
}

.card-header {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}
</style>
