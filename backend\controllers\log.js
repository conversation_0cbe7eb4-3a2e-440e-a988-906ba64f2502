const logService = require('../services/log');

// 获取日志文件内容
async function getLogContent(req, res) {
  try {
    const { filename } = req.params; // 例如: combined.log 或 error.log
    const logContent = await logService.readLogFile(filename);

    if (logContent.success) {
      res.status(200).json({ success: true, content: logContent.content });
    } else {
      res.status(404).json({ success: false, message: logContent.message });
    }
  } catch (error) {
    console.error('获取日志内容失败:', error);
    res.status(500).json({ success: false, message: '获取日志内容失败', error: error.message });
  }
}

// 获取可用日志文件列表
async function getLogFiles(req, res) {
  try {
    const logFiles = await logService.listLogFiles();
    res.status(200).json({ success: true, files: logFiles.files });
  } catch (error) {
    console.error('获取日志文件列表失败:', error);
    res.status(500).json({ success: false, message: '获取日志文件列表失败', error: error.message });
  }
}

module.exports = {
  getLogContent,
  getLogFiles
};