const express = require('express');
const router = express.Router();
const patientController = require('../controllers/patient');
const { authenticateToken } = require('../middlewares/auth'); // 引入认证中间件

// POST /api/patient/info - 提交患者信息 (小程序端提交，不需要认证)
router.post('/info', patientController.submitPatientInfo);

// GET /api/patient/list - 获取患者列表（支持分页、搜索）
router.get('/list', authenticateToken, patientController.getPatientList);

// GET /api/patient/:id - 获取患者详情
router.get('/:id', authenticateToken, patientController.getPatientDetail);

// PUT /api/patient/:id - 更新患者信息
router.put('/:id', authenticateToken, patientController.updatePatient);

// DELETE /api/patient/:id - 删除患者信息
router.delete('/:id', authenticateToken, patientController.deletePatient);

module.exports = router;