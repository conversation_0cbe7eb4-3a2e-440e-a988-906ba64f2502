{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"archiver": "^7.0.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-status-monitor": "^1.3.4", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "mysql2": "^3.14.1", "node-cron": "^3.0.3", "prom-client": "^15.1.3", "response-time": "^2.3.3", "sequelize": "^6.37.7", "winston": "^3.17.0"}}