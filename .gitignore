# Backend specific ignores
backend/logs/**/*
!backend/logs/**/
!backend/logs/**/.gitkeep

backend/uploads/**/*
!backend/uploads/**/
!backend/uploads/**/.gitkeep

# Upload files - 统一上传文件夹
# Ignore all files within the 'upload' directory and its subdirectories,
# but keep the directory structure. An empty .gitkeep file can be added to
# empty directories to ensure they are tracked by Git.
upload/**/*
!upload/**/
!upload/**/.gitkeep

# Log files - 统一日志文件夹
# Ignore all files within the 'log' directory and its subdirectories,
# but keep the directory structure. An empty .gitkeep file can be added to
# empty directories to ensure they are tracked by Git.
log/**/*
!log/**/
!log/**/.gitkeep

# Common temporary and cache files
*.tmp
*.temp
*.cache
.DS_Store
Thumbs.db

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Node.js (if applicable)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python (if applicable)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
# Environment variables
# Ignore local environment files to prevent sensitive data from being committed.
# Use .env.local for local overrides.
*.env.local

# Java (if applicable)
*.class
*.jar
*.war
*.ear
target/

# .NET (if applicable)
bin/
obj/
*.user
*.suo
*.cache

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
