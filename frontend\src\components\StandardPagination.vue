<template>
  <div class="standard-pagination">
    <el-pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      :total="total"
      :layout="layout"
      :background="background"
      :small="small"
      :disabled="disabled"
      :hide-on-single-page="hideOnSinglePage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @prev-click="handlePrevClick"
      @next-click="handleNextClick"
    />
    
    <!-- 分页信息 -->
    <div v-if="showInfo" class="pagination-info">
      <span class="info-text">
        共 {{ total }} 条记录，
        第 {{ currentPage }} / {{ totalPages }} 页，
        每页显示 {{ pageSize }} 条
      </span>
    </div>
  </div>
</template>

<script>
import { defineComponent, computed } from 'vue'

export default defineComponent({
  name: 'StandardPagination',
  props: {
    // 当前页码
    currentPage: {
      type: Number,
      default: 1
    },
    // 每页显示条目个数
    pageSize: {
      type: Number,
      default: 10
    },
    // 每页显示个数选择器的选项设置
    pageSizes: {
      type: Array,
      default: () => [10, 20, 50, 100]
    },
    // 总条目数
    total: {
      type: Number,
      default: 0
    },
    // 组件布局，子组件名用逗号分隔
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    // 是否为分页按钮添加背景色
    background: {
      type: Boolean,
      default: true
    },
    // 是否使用小型分页样式
    small: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 只有一页时是否隐藏
    hideOnSinglePage: {
      type: Boolean,
      default: false
    },
    // 是否显示分页信息
    showInfo: {
      type: Boolean,
      default: false
    },
    // 自定义样式类名
    customClass: {
      type: String,
      default: ''
    }
  },
  emits: ['size-change', 'current-change', 'prev-click', 'next-click'],
  setup(props, { emit }) {
    // 计算总页数
    const totalPages = computed(() => {
      return Math.ceil(props.total / props.pageSize)
    })
    
    // 页码改变事件
    const handleCurrentChange = (page) => {
      emit('current-change', page)
    }
    
    // 每页条数改变事件
    const handleSizeChange = (size) => {
      emit('size-change', size)
    }
    
    // 上一页点击事件
    const handlePrevClick = (page) => {
      emit('prev-click', page)
    }
    
    // 下一页点击事件
    const handleNextClick = (page) => {
      emit('next-click', page)
    }
    
    return {
      totalPages,
      handleCurrentChange,
      handleSizeChange,
      handlePrevClick,
      handleNextClick
    }
  }
})
</script>

<style scoped>
.standard-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.pagination-info {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.info-text {
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .standard-pagination {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
  }
  
  .pagination-info {
    order: -1;
  }
}

/* Element Plus 分页组件样式覆盖 */
:deep(.el-pagination) {
  --el-pagination-font-size: var(--font-size-sm);
  --el-pagination-bg-color: var(--bg-primary);
  --el-pagination-text-color: var(--text-primary);
  --el-pagination-border-radius: var(--radius-sm);
  --el-pagination-button-color: var(--text-secondary);
  --el-pagination-button-bg-color: var(--bg-primary);
  --el-pagination-button-disabled-color: var(--text-disabled);
  --el-pagination-button-disabled-bg-color: var(--bg-disabled);
  --el-pagination-hover-color: var(--primary-color);
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.el-pagination .el-pager li) {
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  margin: 0 2px;
  transition: var(--transition-normal);
}

:deep(.el-pagination .el-pager li:hover) {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.el-pagination .el-pager li.is-active) {
  color: var(--bg-primary);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

:deep(.el-pagination .el-pagination__sizes) {
  margin-right: var(--spacing-md);
}

:deep(.el-pagination .el-pagination__total) {
  margin-right: var(--spacing-md);
  color: var(--text-secondary);
  font-weight: var(--font-weight-normal);
}

:deep(.el-pagination .el-pagination__jump) {
  margin-left: var(--spacing-md);
  color: var(--text-secondary);
}

:deep(.el-pagination .el-input__wrapper) {
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-primary);
  transition: var(--transition-normal);
}

:deep(.el-pagination .el-input__wrapper:hover) {
  border-color: var(--primary-color);
}

:deep(.el-pagination .el-input__wrapper.is-focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 紧凑模式样式 */
:deep(.el-pagination.is-small) {
  --el-pagination-font-size: var(--font-size-xs);
}

:deep(.el-pagination.is-small .btn-prev),
:deep(.el-pagination.is-small .btn-next),
:deep(.el-pagination.is-small .el-pager li) {
  min-width: 28px;
  height: 28px;
  line-height: 26px;
}
</style>
