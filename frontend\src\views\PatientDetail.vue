<template>
  <ContentContainer
    :title="`患者详情 - ${patient.name || '加载中...'}`"
    description="查看患者的详细信息和相关医疗记录"
    :show-header="true"
    :breadcrumbs="breadcrumbs"
    :show-breadcrumb="true"
  >
    <!-- 操作按钮 -->
    <template #header-right>
      <el-button
        type="primary"
        @click="handleEdit"
        icon="Edit"
      >
        编辑
      </el-button>
      <el-button
        type="danger"
        @click="handleDelete"
        icon="Delete"
      >
        删除
      </el-button>
    </template>

    <!-- 患者基本信息 -->
    <div class="patient-info-section">
      <el-card v-loading="loading">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag v-if="patient.patient_id" type="info">
              ID: {{ patient.patient_id }}
            </el-tag>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">
            {{ patient.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="年龄">
            {{ patient.age || '-' }} 岁
          </el-descriptions-item>
          <el-descriptions-item label="病史" span="2">
            <div class="text-content">
              {{ patient.medical_history || '无' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="症状" span="2">
            <div class="text-content">
              {{ patient.symptoms || '无' }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(patient.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(patient.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- 相关图片 -->
    <div class="patient-images-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>相关图片</span>
            <el-button
              type="primary"
              size="small"
              @click="handleUploadImage"
              icon="Upload"
            >
              上传图片
            </el-button>
          </div>
        </template>

        <div v-if="patient.images && patient.images.length > 0" class="images-grid">
          <div
            v-for="image in patient.images"
            :key="image.image_id"
            class="image-item"
          >
            <img
              :src="getImageUrl(image.image_url)"
              :alt="image.original_name"
              class="image-preview"
              @click="previewImage(image)"
            />
            <div class="image-info">
              <div class="image-name">{{ image.original_name || '未知文件' }}</div>
              <div class="image-meta">
                {{ formatFileSize(image.file_size) }} |
                {{ formatDate(image.created_at) }}
              </div>
              <div class="image-actions">
                <el-button
                  link
                  size="small"
                  @click="deleteImage(image.image_id)"
                  class="text-error"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <el-empty
          v-else
          description="该患者暂无图片资料"
          :image-size="100"
        >
          <el-button type="primary" @click="handleUploadImage">
            上传第一张图片
          </el-button>
        </el-empty>
      </el-card>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="currentImage?.original_name || '图片预览'"
      width="80%"
      center
    >
      <div class="image-preview-container">
        <img
          v-if="currentImage"
          :src="getImageUrl(currentImage.image_url)"
          :alt="currentImage.original_name"
          class="preview-image"
        />
      </div>
    </el-dialog>
  </ContentContainer>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import ContentContainer from '../components/ContentContainer.vue'
import api from '../api'

export default defineComponent({
  name: 'PatientDetail',
  components: {
    ContentContainer
  },
  setup() {
    const route = useRoute()
    const router = useRouter()

    const loading = ref(false)
    const patient = ref({})
    const dialogVisible = ref(false)
    const currentImage = ref(null)

    // 面包屑导航
    const breadcrumbs = computed(() => [
      { title: '首页', path: '/' },
      { title: '患者管理', path: '/patients' },
      { title: '患者详情', path: `/patient/${route.params.id}` }
    ])

    const fetchPatientDetail = async () => {
      loading.value = true
      try {
        const response = await api.get(`/patient/${route.params.id}`)

        if (response.patient) {
          patient.value = response.patient
        } else {
          ElMessage.error('获取患者详情失败')
        }
      } catch (error) {
        console.error('获取患者详情失败:', error)
        ElMessage.error('获取患者详情失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 编辑患者
    const handleEdit = () => {
      router.push(`/patient/${route.params.id}/edit`)
    }

    // 删除患者
    const handleDelete = async () => {
      try {
        await ElMessageBox.confirm(
          `确定要删除患者 "${patient.value.name}" 的信息吗？此操作不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        const response = await api.delete(`/patient/${route.params.id}`)

        if (response.message) {
          ElMessage.success('患者信息删除成功')
          router.push('/patients')
        } else {
          ElMessage.error('删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除患者失败:', error)
          ElMessage.error('删除失败，请稍后重试')
        }
      }
    }

    // 上传图片
    const handleUploadImage = () => {
      ElMessage.info('上传图片功能开发中...')
    }

    const getImageUrl = (url) => {
      if (!url) return ''
      if (url.startsWith('http')) return url
      return `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'}${url}`
    }

    const previewImage = (image) => {
      currentImage.value = image
      dialogVisible.value = true
    }

    const deleteImage = async (imageId) => {
      try {
        await ElMessageBox.confirm(
          '确定要删除这张图片吗？此操作不可恢复。',
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )

        const response = await api.delete(`/image/${imageId}`)

        if (response.success) {
          ElMessage.success('图片删除成功')
          fetchPatientDetail() // 重新获取患者详情以更新图片列表
        } else {
          ElMessage.error('删除图片失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除图片失败:', error)
          ElMessage.error('删除图片失败，请稍后重试')
        }
      }
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      return new Date(dateString).toLocaleString('zh-CN')
    }

    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    onMounted(() => {
      fetchPatientDetail();
    });

    return {
      loading,
      patient,
      breadcrumbs,
      dialogVisible,
      currentImage,
      handleEdit,
      handleDelete,
      handleUploadImage,
      getImageUrl,
      previewImage,
      deleteImage,
      formatDate,
      formatFileSize
    }
  }
});
</script>

<style scoped>
.patient-info-section,
.patient-images-section {
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-content {
  line-height: var(--line-height-lg);
  white-space: pre-wrap;
  word-break: break-word;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.image-item {
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: var(--transition-normal);
}

.image-item:hover {
  box-shadow: var(--shadow-md);
}

.image-preview {
  width: 100%;
  height: 150px;
  object-fit: cover;
  cursor: pointer;
}

.image-info {
  padding: var(--spacing-sm);
}

.image-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-meta {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.image-actions {
  text-align: right;
}

.text-error {
  color: var(--error-color);
}

.image-preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: var(--radius-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-sm);
  }

  :deep(.el-descriptions) {
    --el-descriptions-item-bordered-label-background: var(--bg-tertiary);
  }
}
</style>