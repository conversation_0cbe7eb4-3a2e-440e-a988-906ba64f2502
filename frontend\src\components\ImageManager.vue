<template>
  <div class="image-manager">
    <!-- 工具栏 -->
    <div class="image-toolbar">
      <div class="toolbar-left">
        <div class="view-controls">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button value="grid">网格视图</el-radio-button>
            <el-radio-button value="list">列表视图</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="toolbar-right">
        <slot name="toolbar-actions">
          <el-button
            v-if="allowUpload"
            type="primary"
            @click="handleUpload"
            icon="Upload"
          >
            上传图片
          </el-button>
        </slot>
      </div>
    </div>

    <!-- 网格视图 -->
    <div v-if="viewMode === 'grid'" class="image-grid" v-loading="loading">
      <!-- 调试信息（仅开发模式） -->
      <div v-if="showDebugInfo" class="debug-info">
        <strong>调试信息：</strong>
        总共 {{ images.length }} 张图片，
        当前视图模式：{{ viewMode }}
      </div>

      <div
        v-for="(image, index) in images"
        :key="`grid-${image.image_id}-${index}`"
        class="image-card"
        :data-index="index"
        :data-id="image.image_id"
      >
        <div class="image-wrapper">
          <img
            :src="getImageUrl(image.image_url)"
            :alt="image.original_name"
            class="image-preview"
            @click="previewImage(getImageUrl(image.image_url), image)"
            @error="handleImageError"
          />
          <!-- 如果图片加载失败，显示占位符 -->
          <div v-if="!getImageUrl(image.image_url)" class="image-placeholder">
            <span>图片URL缺失</span>
          </div>
          <div class="image-overlay">
            <el-button
              type="primary"
              size="small"
              @click="previewImage(getImageUrl(image.image_url), image)"
              icon="View"
              circle
            />
            <el-button
              v-if="allowDelete"
              type="danger"
              size="small"
              @click="deleteImage(image)"
              icon="Delete"
              circle
            />
          </div>
        </div>
        <div class="image-info">
          <div class="image-title">{{ image.original_name }}</div>
          <div class="image-meta">
            <span class="meta-item">ID: {{ image.image_id }}</span>
            <span v-if="showPatientInfo" class="meta-item">患者ID: {{ image.patient_id || '未关联' }}</span>
            <span class="meta-item">大小: {{ formatFileSize(image.file_size) }}</span>
            <span class="meta-item">上传时间: {{ formatDate(image.created_at) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 列表视图 -->
    <DataTable
      v-else
      :data="images"
      :columns="tableColumns"
      :loading="loading"
      :pagination="pagination"
      :title="tableTitle"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      @refresh="refreshImages"
    >
      <!-- 自定义图片预览列 -->
      <template #image_preview="{ row }">
        <img
          :src="getImageUrl(row.image_url)"
          :alt="row.original_name"
          class="table-image-preview"
          @click="previewImage(getImageUrl(row.image_url), row)"
          @error="handleImageError"
        />
      </template>

      <!-- 自定义操作列 -->
      <template #actions="{ row }">
        <el-button
          type="text"
          size="small"
          @click="previewImage(getImageUrl(row.image_url), row)"
          icon="View"
        >
          预览
        </el-button>
        <el-button
          v-if="allowDownload"
          type="text"
          size="small"
          @click="downloadImage(row)"
          icon="Download"
        >
          下载
        </el-button>
        <el-button
          v-if="allowDelete"
          type="text"
          size="small"
          @click="deleteImage(row)"
          class="text-error"
          icon="Delete"
        >
          删除
        </el-button>
      </template>
    </DataTable>

    <!-- 空状态 -->
    <el-empty
      v-if="!loading && images.length === 0"
      :description="emptyDescription"
      class="empty-state"
    >
      <el-button v-if="allowUpload" type="primary" @click="handleUpload">
        {{ emptyActionText }}
      </el-button>
    </el-empty>

    <!-- 分页组件 -->
    <StandardPagination
      v-if="showPagination && images.length > 0"
      :current-page="pagination.page"
      :page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="pageSizes"
      :show-info="true"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="currentImage?.original_name || '图片预览'"
      width="80%"
      center
    >
      <div class="preview-container">
        <img
          :src="dialogImageUrl"
          :alt="currentImage?.original_name"
          class="preview-image"
        />
        <div v-if="currentImage && showImageDetails" class="preview-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名">{{ currentImage.original_name }}</el-descriptions-item>
            <el-descriptions-item label="文件大小">{{ formatFileSize(currentImage.file_size) }}</el-descriptions-item>
            <el-descriptions-item v-if="showPatientInfo" label="患者ID">{{ currentImage.patient_id || '未关联' }}</el-descriptions-item>
            <el-descriptions-item label="上传时间">{{ formatDate(currentImage.created_at) }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button
          v-if="currentImage && allowDownload"
          type="primary"
          @click="downloadImage(currentImage)"
        >
          下载
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import DataTable from './DataTable.vue';
import StandardPagination from './StandardPagination.vue';

export default defineComponent({
  name: 'ImageManager',
  components: {
    DataTable,
    StandardPagination
  },
  props: {
    // 图片数据
    images: {
      type: Array,
      default: () => []
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 分页配置
    pagination: {
      type: Object,
      default: () => ({
        page: 1,
        pageSize: 24,
        total: 0
      })
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 分页大小选项
    pageSizes: {
      type: Array,
      default: () => [12, 24, 48, 96]
    },
    // 是否允许上传
    allowUpload: {
      type: Boolean,
      default: true
    },
    // 是否允许删除
    allowDelete: {
      type: Boolean,
      default: true
    },
    // 是否允许下载
    allowDownload: {
      type: Boolean,
      default: true
    },
    // 是否显示患者信息
    showPatientInfo: {
      type: Boolean,
      default: true
    },
    // 是否显示图片详情
    showImageDetails: {
      type: Boolean,
      default: true
    },
    // 是否显示调试信息
    showDebugInfo: {
      type: Boolean,
      default: false
    },
    // 空状态描述
    emptyDescription: {
      type: String,
      default: '暂无图片资料'
    },
    // 空状态操作按钮文本
    emptyActionText: {
      type: String,
      default: '上传第一张图片'
    },
    // 表格标题
    tableTitle: {
      type: String,
      default: '图片列表'
    },
    // API基础URL
    apiBaseUrl: {
      type: String,
      default: () => import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
    }
  },
  emits: [
    'upload',
    'delete',
    'download',
    'preview',
    'current-change',
    'size-change',
    'refresh'
  ],
  setup(props, { emit }) {
    const viewMode = ref('grid');
    const dialogVisible = ref(false);
    const dialogImageUrl = ref('');
    const currentImage = ref(null);

    // 表格列配置
    const tableColumns = computed(() => {
      const columns = [
        {
          prop: 'image_preview',
          label: '预览',
          width: 80,
          align: 'center',
          slot: 'image_preview'
        },
        {
          prop: 'original_name',
          label: '文件名',
          minWidth: 150,
          showOverflowTooltip: true
        }
      ];

      if (props.showPatientInfo) {
        columns.push({
          prop: 'patient_id',
          label: '患者ID',
          width: 100,
          align: 'center',
          formatter: (row) => row.patient_id || '未关联'
        });
      }

      columns.push(
        {
          prop: 'file_size',
          label: '文件大小',
          width: 100,
          align: 'center',
          formatter: (row) => formatFileSize(row.file_size)
        },
        {
          prop: 'created_at',
          label: '上传时间',
          width: 180,
          align: 'center',
          formatter: (row) => formatDate(row.created_at)
        }
      );

      if (props.allowDelete || props.allowDownload) {
        columns.push({
          prop: 'actions',
          label: '操作',
          width: 150,
          align: 'center',
          slot: 'actions'
        });
      }

      return columns;
    });

    // 获取图片URL
    const getImageUrl = (url) => {
      if (!url) {
        console.warn('图片URL为空');
        return '';
      }

      if (url.startsWith('http')) {
        return url;
      }

      return `${props.apiBaseUrl}${url}`;
    };

    // 图片预览
    const previewImage = (url, image = null) => {
      dialogImageUrl.value = url;
      currentImage.value = image;
      dialogVisible.value = true;
      emit('preview', { url, image });
    };

    // 删除图片
    const deleteImage = async (image) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除图片 "${image.original_name}" 吗？此操作不可恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        emit('delete', image);
      } catch (error) {
        // 用户取消删除
      }
    };

    // 下载图片
    const downloadImage = (image) => {
      const url = getImageUrl(image.image_url);
      const link = document.createElement('a');
      link.href = url;
      link.download = image.original_name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      emit('download', image);
    };

    // 上传图片
    const handleUpload = () => {
      emit('upload');
    };

    // 刷新图片列表
    const refreshImages = () => {
      emit('refresh');
    };

    // 分页处理
    const handleSizeChange = (newSize) => {
      emit('size-change', newSize);
    };

    const handleCurrentChange = (newPage) => {
      emit('current-change', newPage);
    };

    // 图片加载错误处理
    const handleImageError = (event) => {
      console.error('图片加载失败:', event.target.src);
      const canvas = document.createElement('canvas');
      canvas.width = 200;
      canvas.height = 200;
      const ctx = canvas.getContext('2d');

      ctx.fillStyle = '#f5f5f5';
      ctx.fillRect(0, 0, 200, 200);
      ctx.strokeStyle = '#ddd';
      ctx.lineWidth = 2;
      ctx.strokeRect(1, 1, 198, 198);
      ctx.fillStyle = '#999';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('图片加载失败', 100, 90);
      ctx.fillText('Image Load Error', 100, 110);

      event.target.src = canvas.toDataURL();
      event.target.style.objectFit = 'contain';
    };

    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-';
      return new Date(dateString).toLocaleString('zh-CN');
    };

    return {
      viewMode,
      dialogVisible,
      dialogImageUrl,
      currentImage,
      tableColumns,
      getImageUrl,
      previewImage,
      deleteImage,
      downloadImage,
      handleUpload,
      refreshImages,
      handleSizeChange,
      handleCurrentChange,
      handleImageError,
      formatFileSize,
      formatDate
    };
  }
});
</script>

<style scoped>
.image-manager {
  width: 100%;
}

.image-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-secondary);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.toolbar-right {
  display: flex;
  gap: var(--spacing-sm);
}

.view-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* 网格视图样式 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-md);
  min-height: 200px;
}

.debug-info {
  grid-column: 1 / -1;
  background: #f0f0f0;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.image-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
  min-height: 300px;
}

.image-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: var(--bg-tertiary);
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: var(--transition-normal);
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #999;
  font-size: 14px;
}

.image-preview:hover {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  opacity: 0;
  transition: var(--transition-normal);
}

.image-wrapper:hover .image-overlay {
  opacity: 1;
}

.image-info {
  padding: var(--spacing-md);
}

.image-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.meta-item {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* 列表视图中的图片预览 */
.table-image-preview {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition-normal);
}

.table-image-preview:hover {
  transform: scale(1.1);
}

/* 预览对话框样式 */
.preview-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
  margin: 0 auto;
  display: block;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

.preview-info {
  background: var(--bg-tertiary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
}

/* 空状态样式 */
.empty-state {
  padding: var(--spacing-xxl);
  margin: var(--spacing-lg) 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
  }

  .image-toolbar {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .image-grid {
    grid-template-columns: 1fr;
  }
}

.text-error {
  color: var(--error-color) !important;
}
</style>
