const express = require('express');
const router = express.Router();
const imageController = require('../controllers/image');
const upload = require('../middlewares/upload'); // 引入 Multer 配置
const { authenticateToken } = require('../middlewares/auth'); // 引入认证中间件
const multer = require('multer');

// 错误处理中间件
const handleUploadError = (err, req, res, next) => {
  console.log('=== 上传错误中间件触发 ===');
  console.log('错误类型:', err.constructor.name);
  console.log('错误消息:', err.message);
  console.log('错误堆栈:', err.stack);
  console.log('请求头:', req.headers);
  console.log('请求URL:', req.url);
  console.log('请求方法:', req.method);

  if (err instanceof multer.MulterError) {
    console.error('Multer 错误详情:', {
      code: err.code,
      message: err.message,
      field: err.field
    });
    return res.status(400).json({
      message: `文件上传错误: ${err.message}`,
      code: err.code,
      field: err.field
    });
  } else if (err) {
    console.error('其他上传错误:', err);
    return res.status(500).json({
      message: `上传失败: ${err.message}`,
      error: err.name
    });
  }
  next();
};

// 添加一个简单的测试路由
router.post('/test-upload', (req, res) => {
  console.log('=== 测试上传路由 ===');
  console.log('请求头:', req.headers);
  console.log('请求体:', req.body);
  res.json({ message: '测试路由正常', received: req.body });
});

// POST /api/image/upload - 上传图片 (小程序端上传，不需要认证)
router.post('/upload', upload.array('images', 10), handleUploadError, imageController.uploadImage);

// GET /api/image/list - 获取图片列表
router.get('/list', authenticateToken, imageController.getImageList);

// GET /api/image/:id - 获取图片详情
router.get('/:id', authenticateToken, imageController.getImageDetail);

// DELETE /api/image/:id - 删除图片
router.delete('/:id', authenticateToken, imageController.deleteImage);

module.exports = router;