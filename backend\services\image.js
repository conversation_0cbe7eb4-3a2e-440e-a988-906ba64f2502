const { Image, Patient } = require('../models');
const fs = require('fs');
const path = require('path');

async function createImage(imageData) {
  try {
    const image = await Image.create(imageData);
    return { success: true, image };
  } catch (error) {
    console.error('服务层：创建图片失败:', error);
    return { success: false, message: error.message };
  }
}

async function getAllImages(patient_id, page, limit) {
  try {
    const offset = (page - 1) * limit;
    const whereClause = {};
    if (patient_id) {
      whereClause.patient_id = patient_id;
    }

    const { count, rows: images } = await Image.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    return { success: true, total: count, images };
  } catch (error) {
    console.error('服务层：获取图片列表失败:', error);
    return { success: false, message: error.message };
  }
}

async function getImageById(id) {
  try {
    const image = await Image.findByPk(id, {
      include: [{
        model: Patient,
        as: 'patient'
      }]
    });
    if (!image) {
      return { success: false, message: '未找到该图片。' };
    }
    return { success: true, image };
  } catch (error) {
    console.error('服务层：获取图片详情失败:', error);
    return { success: false, message: error.message };
  }
}

async function deleteImageById(id) {
  try {
    const image = await Image.findByPk(id);
    if (!image) {
      return { success: false, message: '未找到该图片。' };
    }

    // 从文件系统删除图片
    let imagePath;
    if (image.file_path && fs.existsSync(image.file_path)) {
      // 优先使用 file_path 字段（完整路径）
      imagePath = image.file_path;
    } else if (image.image_url) {
      // 如果没有 file_path，从 image_url 构建路径
      const relativePath = image.image_url.startsWith('/') ? image.image_url.substring(1) : image.image_url;
      imagePath = path.join(__dirname, '..', relativePath);
    }

    // 删除文件系统中的图片文件
    if (imagePath && fs.existsSync(imagePath)) {
      try {
        fs.unlinkSync(imagePath);
        console.log('服务层：已删除图片文件:', imagePath);
      } catch (fileError) {
        console.warn('服务层：删除图片文件失败:', fileError.message);
        // 即使文件删除失败，也继续删除数据库记录
      }
    } else {
      console.warn('服务层：图片文件不存在或路径无效:', imagePath);
    }

    await image.destroy();
    return { success: true, message: '图片删除成功。' };
  } catch (error) {
    console.error('服务层：删除图片失败:', error);
    return { success: false, message: error.message };
  }
}

module.exports = {
  createImage,
  getAllImages,
  getImageById,
  deleteImageById
};