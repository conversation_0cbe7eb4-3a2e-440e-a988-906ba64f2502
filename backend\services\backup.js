const { exec } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const cron = require('node-cron');
const logger = require('../utils/logger');

class BackupService {
  constructor() {
    this.backupDir = process.env.BACKUP_DIR || '/backups';
    this.scriptsDir = path.join(__dirname, '../../scripts');
    this.isBackupRunning = false;
    this.backupHistory = [];
    
    // 初始化定时备份
    this.initScheduledBackups();
  }

  // 初始化定时备份任务
  initScheduledBackups() {
    // 每天凌晨2点执行完整备份
    cron.schedule('0 2 * * *', () => {
      this.performBackup('full');
    }, {
      scheduled: true,
      timezone: "Asia/Shanghai"
    });

    // 每4小时执行增量备份
    cron.schedule('0 */4 * * *', () => {
      this.performBackup('incremental');
    }, {
      scheduled: true,
      timezone: "Asia/Shanghai"
    });

    logger.info('Backup scheduler initialized');
  }

  // 执行备份
  async performBackup(type = 'full') {
    if (this.isBackupRunning) {
      logger.warn('Backup is already running, skipping...');
      return { success: false, message: 'Backup already in progress' };
    }

    this.isBackupRunning = true;
    const startTime = Date.now();

    try {
      logger.info(`Starting ${type} backup...`);
      
      const backupScript = path.join(this.scriptsDir, 'backup.sh');
      const command = `bash "${backupScript}" "${process.env.DB_NAME || 'health_uplink'}" "${type}"`;
      
      const result = await this.executeCommand(command);
      
      const duration = Date.now() - startTime;
      const backupInfo = {
        type,
        timestamp: new Date().toISOString(),
        duration,
        success: true,
        output: result.stdout
      };

      this.backupHistory.unshift(backupInfo);
      
      // 保持最近50条备份记录
      if (this.backupHistory.length > 50) {
        this.backupHistory = this.backupHistory.slice(0, 50);
      }

      logger.info(`${type} backup completed successfully in ${duration}ms`);
      
      return {
        success: true,
        message: `${type} backup completed successfully`,
        duration,
        timestamp: backupInfo.timestamp
      };

    } catch (error) {
      logger.error(`Backup failed: ${error.message}`);
      
      const backupInfo = {
        type,
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime,
        success: false,
        error: error.message
      };

      this.backupHistory.unshift(backupInfo);

      return {
        success: false,
        message: `Backup failed: ${error.message}`,
        error: error.message
      };
    } finally {
      this.isBackupRunning = false;
    }
  }

  // 获取备份列表
  async getBackupList() {
    try {
      const backupTypes = ['full', 'incremental'];
      const backups = [];

      for (const type of backupTypes) {
        const typeDir = path.join(this.backupDir, type);
        
        try {
          const files = await fs.readdir(typeDir);
          
          for (const file of files) {
            if (file.endsWith('.sql.gz')) {
              const filePath = path.join(typeDir, file);
              const stats = await fs.stat(filePath);
              
              backups.push({
                filename: file,
                type,
                path: filePath,
                size: stats.size,
                created: stats.mtime,
                sizeFormatted: this.formatFileSize(stats.size)
              });
            }
          }
        } catch (error) {
          logger.warn(`Failed to read backup directory ${typeDir}: ${error.message}`);
        }
      }

      // 按创建时间排序
      backups.sort((a, b) => new Date(b.created) - new Date(a.created));

      return backups;
    } catch (error) {
      logger.error(`Failed to get backup list: ${error.message}`);
      throw error;
    }
  }

  // 删除备份文件
  async deleteBackup(filename) {
    try {
      const backups = await this.getBackupList();
      const backup = backups.find(b => b.filename === filename);
      
      if (!backup) {
        throw new Error('Backup file not found');
      }

      await fs.unlink(backup.path);
      logger.info(`Backup file deleted: ${filename}`);
      
      return { success: true, message: 'Backup deleted successfully' };
    } catch (error) {
      logger.error(`Failed to delete backup: ${error.message}`);
      throw error;
    }
  }

  // 恢复数据库
  async restoreDatabase(filename, targetDatabase) {
    try {
      const backups = await this.getBackupList();
      const backup = backups.find(b => b.filename === filename);
      
      if (!backup) {
        throw new Error('Backup file not found');
      }

      logger.info(`Starting database restore from ${filename} to ${targetDatabase}`);
      
      const restoreScript = path.join(this.scriptsDir, 'restore.sh');
      const command = `bash "${restoreScript}" "${backup.path}" "${targetDatabase}"`;
      
      const result = await this.executeCommand(command, 300000); // 5分钟超时
      
      logger.info(`Database restore completed successfully`);
      
      return {
        success: true,
        message: 'Database restored successfully',
        output: result.stdout
      };

    } catch (error) {
      logger.error(`Database restore failed: ${error.message}`);
      throw error;
    }
  }

  // 获取备份历史
  getBackupHistory() {
    return this.backupHistory;
  }

  // 获取备份状态
  getBackupStatus() {
    return {
      isRunning: this.isBackupRunning,
      lastBackup: this.backupHistory.length > 0 ? this.backupHistory[0] : null,
      totalBackups: this.backupHistory.length
    };
  }

  // 验证备份文件
  async verifyBackup(filename) {
    try {
      const backups = await this.getBackupList();
      const backup = backups.find(b => b.filename === filename);
      
      if (!backup) {
        throw new Error('Backup file not found');
      }

      // 检查文件完整性
      const command = `gzip -t "${backup.path}"`;
      await this.executeCommand(command);
      
      return {
        success: true,
        message: 'Backup file is valid',
        filename,
        size: backup.sizeFormatted
      };

    } catch (error) {
      logger.error(`Backup verification failed: ${error.message}`);
      throw error;
    }
  }

  // 执行命令
  executeCommand(command, timeout = 60000) {
    return new Promise((resolve, reject) => {
      exec(command, { timeout }, (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`Command failed: ${error.message}\nStderr: ${stderr}`));
        } else {
          resolve({ stdout, stderr });
        }
      });
    });
  }

  // 格式化文件大小
  formatFileSize(bytes) {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}

module.exports = new BackupService();
