# 数据库配置 - Docker 环境
DB_HOST=mysql # 使用 docker-compose 的服务名
DB_PORT=3306
DB_NAME=health_uplink
DB_USER=health_user
DB_PASSWORD=health_user

# JWT配置
JWT_SECRET=docker_secret_key_for_testing

# 服务器配置
PORT=3000
NODE_ENV=production # Docker 环境通常模拟生产环境
API_BASE_URL=http://localhost:3000

# 数据库同步配置 (Docker 启动时可以禁用或使用安全模式)
DB_SYNC_ENABLED=true
DB_SYNC_FORCE=false
DB_SYNC_ALTER=false

# 文件上传配置
UPLOAD_DIR=uploads/images
MAX_FILE_SIZE=10485760

# 日志配置
LOG_LEVEL=info
LOG_DIR=log/backend

# 管理员默认账户
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123

# API限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
