const express = require('express');
const router = express.Router();
const exportController = require('../controllers/export');
const { authenticateToken } = require('../middlewares/auth'); // 引入认证中间件

// GET /api/export/csv - 导出患者信息为 CSV 格式
router.get('/csv', authenticateToken, exportController.exportPatientsToCsv);

// GET /api/export/images - 导出图片为 ZIP 压缩包
router.get('/images', authenticateToken, exportController.exportImagesToZip);

// GET /api/export/all - 导出完整数据包（CSV + 图片ZIP）
router.get('/all', authenticateToken, exportController.exportAllData);

module.exports = router;