<!--pages/upload/upload.wxml-->
<wxs module="utils">
  function getImageUrl(path) {
    // 严格检查path是否为有效字符串
    if (!path || typeof path !== 'string' || path === '') {
      console.log('WXS: 路径为空或无效');
      return '';
    }

    // 转换为字符串确保安全
    var pathStr = '' + path;
    console.log('WXS: 处理路径 = ' + pathStr);

    // 如果是HTTP URL，直接返回
    if (pathStr.length > 4 && pathStr.substring(0, 4) === 'http') {
      console.log('WXS: HTTP URL，直接返回');
      return pathStr;
    }

    // 如果是微信临时文件路径（各种格式）
    if (pathStr.indexOf('wxfile://') === 0 ||
        pathStr.indexOf('tmp_') === 0 ||
        pathStr.indexOf('temp/') !== -1 ||
        pathStr.indexOf('store_') === 0 ||
        pathStr.indexOf('/var/mobile/') !== -1 ||
        pathStr.indexOf('file://') === 0) {
      console.log('WXS: 微信临时文件，直接返回');
      return pathStr;
    }

    // 如果是相对路径，拼接服务器地址
    if (pathStr.length > 0 && pathStr.substring(0, 1) === '/') {
      console.log('WXS: 相对路径，拼接服务器地址');
      return 'http://localhost:3000' + pathStr;
    }

    console.log('WXS: 未知路径格式，直接返回: ' + pathStr);
    return pathStr;
  }
  module.exports.getImageUrl = getImageUrl;
</wxs>

<page-meta>
  <navigation-bar title="图片上传"></navigation-bar>
</page-meta>
<view class="container" style="padding-top: {{contentPaddingTop}}px;" bindtouchstart="onTouchStart" bindtouchmove="onTouchMove" bindtouchend="onTouchEnd">
  <view class="type-selection-section">
    <picker bindchange="bindPickerChange" value="{{imageTypes.indexOf(selectedType)}}" range="{{imageTypes}}">
      <view class="picker-container">
        <text class="picker-label">当前选择类型:</text>
        <text class="selected-type">{{selectedType}}</text>
        <text class="arrow">▼</text>
      </view>
    </picker>
  </view>

  <view class="image-upload-section">
    <button class="choose-image-button" bindtap="chooseImage">选择图片</button>
    <button class="batch-upload-button" bindtap="batchUpload">批量上传</button>
    <button class="clear-all-button" bindtap="clearAllImages" wx:if="{{images.length > 0}}">清空所有</button>
  </view>

  <!-- 临时调试信息 -->
  <view class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;">
    <text>🔍 调试信息：图片数量 = {{images.length}}</text>
    <view wx:for="{{images}}" wx:key="path" wx:for-index="idx">
      <text>📷 图片{{idx + 1}}: {{item.uploaded ? '✅已上传' : '⏳待上传'}}</text>
      <text>📍 原始路径: {{item.path}}</text>
      <text>🔗 处理后URL: {{utils.getImageUrl(item.path)}}</text>
      <text>📝 类型: {{item.type}}</text>
    </view>
  </view>

  <!-- 测试图片显示 -->
  <view class="test-section" style="background: #e6f7ff; padding: 10px; margin: 10px 0;">
    <text style="font-weight: bold;">🧪 测试区域：</text>
    <view style="display: flex; gap: 10px; margin-top: 10px;">
      <!-- 测试本地图片 -->
      <view style="width: 60px; height: 60px; border: 1px solid #ccc;">
        <text style="font-size: 10px;">本地图标</text>
        <image src="/images/icon-upload.png" mode="aspectFit" style="width: 100%; height: 45px;" />
      </view>
      <!-- 测试第一张选择的图片 -->
      <view style="width: 60px; height: 60px; border: 1px solid #ccc;" wx:if="{{images.length > 0}}">
        <text style="font-size: 10px;">第1张图</text>
        <image src="{{utils.getImageUrl(images[0].path)}}" mode="aspectFit" style="width: 100%; height: 45px;" binderror="onImageError" bindload="onImageLoad" />
      </view>
    </view>
  </view>

  <view class="image-grid" wx:if="{{images.length > 0}}">
    <text style="font-size: 14px; margin-bottom: 10px; display: block;">📋 图片列表 ({{images.length}}张):</text>
    <block wx:for="{{images}}" wx:key="path">
      <view class="image-item">
        <!-- 添加调试信息显示在每个图片项上 -->
        <view style="position: absolute; top: 0; left: 0; background: rgba(0,0,0,0.7); color: white; font-size: 10px; padding: 2px; z-index: 10;">
          {{index + 1}}: {{utils.getImageUrl(item.path) ? '✅' : '❌'}}
        </view>

        <image
          src="{{utils.getImageUrl(item.path)}}"
          mode="aspectFill"
          bindtap="previewImage"
          data-src="{{utils.getImageUrl(item.path)}}"
          binderror="onImageError"
          bindload="onImageLoad"
          data-index="{{index}}"
          style="width: 100%; height: 100%;"
          lazy-load="{{false}}"
        />

        <!-- 如果图片加载失败，显示占位符 -->
        <view wx:if="{{!utils.getImageUrl(item.path)}}" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
          <text style="font-size: 12px; color: #999;">图片路径无效</text>
        </view>

        <view class="image-info">
          <text class="image-type">{{item.type}}</text>
          <text class="upload-status {{item.uploaded ? 'status-success' : item.uploading ? 'status-uploading' : 'status-pending'}}">
            {{item.uploaded ? '已上传' : item.uploading ? '上传中' : '待上传'}}
          </text>
          <view wx:if="{{item.uploading}}" class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress}}%"></view>
          </view>
        </view>
        <view class="delete-button" bindtap="deleteImage" data-index="{{index}}">×</view>
      </view>
    </block>
  </view>
  <view wx:else class="empty-state">
    <image class="empty-image" src="/images/icon-upload.png" mode="aspectFit"></image>
    <text class="empty-text">暂无图片，点击上方“选择图片”添加</text>
  </view>
</view>
